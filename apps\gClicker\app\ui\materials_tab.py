from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QPushButton,
    QComboBox, QHBoxLayout, QGroupBox, QGridLayout, QMessageBox
)
from PyQt6.QtGui import QFont
from PyQt6.QtCore import Qt

from apps.gClicker.app.core.materials.manager import MaterialManager
from apps.gClicker.app.core.materials.tiers import MaterialTier
from apps.gClicker.app.core.power.manager import PowerManager


POWER_COST_PER_REFINE = 10
POWER_COST_PER_PURIFY = 15

class MaterialsTab(QWidget):
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.material_manager = MaterialManager(self.user_data)
        self.power_manager = PowerManager(self.user_data)

        self.current_tier = MaterialTier.BASIC

        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Tier selector dropdown
        tier_layout = QHBoxLayout()
        tier_label = QLabel("Select Material Tier:")
        tier_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        tier_layout.addWidget(tier_label)

        self.tier_combo = QComboBox()
        for tier in MaterialTier:
            self.tier_combo.addItem(tier.name.capitalize(), tier)
        self.tier_combo.currentIndexChanged.connect(self.on_tier_change)
        tier_layout.addWidget(self.tier_combo)
        tier_layout.addStretch()

        layout.addLayout(tier_layout)

        # Power display
        self.power_label = QLabel()
        self.power_label.setFont(QFont("Arial", 11))
        layout.addWidget(self.power_label)

        # Scroll area or group box for materials list
        self.materials_group = QGroupBox("Materials")
        self.materials_layout = QGridLayout()
        self.materials_group.setLayout(self.materials_layout)
        layout.addWidget(self.materials_group)

        self.update_power_display()
        self.update_materials_list()

    def on_tier_change(self, index):
        self.current_tier = self.tier_combo.currentData()
        self.update_materials_list()

    def update_power_display(self):
        current_power = self.power_manager.get_power()
        max_power = self.power_manager.get_max_power()
        self.power_label.setText(f"Power: {current_power} / {max_power}")

    def update_materials_list(self):
        # Clear old widgets
        for i in reversed(range(self.materials_layout.count())):
            widget = self.materials_layout.itemAt(i).widget()
            if widget:
                widget.setParent(None)

        materials = self.material_manager.get_materials_by_tier(self.current_tier)
        if not materials:
            no_label = QLabel("No materials found for this tier.")
            self.materials_layout.addWidget(no_label, 0, 0)
            return

        # Create UI rows per material
        for row, material in enumerate(materials):
            self.create_material_row(row, material)

    def create_material_row(self, row, material):
        name_label = QLabel(material.name.capitalize())
        name_label.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        self.materials_layout.addWidget(name_label, row, 0)

        totals = material.get_totals()
        amounts_text = f"Raw: {totals['raw']} | Refined: {totals['refined']} | Pure: {totals['pure']}"
        amounts_label = QLabel(amounts_text)
        self.materials_layout.addWidget(amounts_label, row, 1)

        refine_btn = QPushButton("Refine")
        refine_btn.clicked.connect(lambda _, m=material: self.refine_material(m))
        self.materials_layout.addWidget(refine_btn, row, 2)

        purify_btn = QPushButton("Purify")
        purify_btn.clicked.connect(lambda _, m=material: self.purify_material(m))
        self.materials_layout.addWidget(purify_btn, row, 3)

    def refine_material(self, material):
        if self.power_manager.get_power() < POWER_COST_PER_REFINE:
            QMessageBox.warning(self, "Insufficient Power", "Not enough power to refine.")
            return

        refined_amount = material.refine(20)
        if refined_amount == 0:
            QMessageBox.information(self, "Refine Failed", "Not enough raw material to refine.")
            return

        self.power_manager.consume_power(POWER_COST_PER_REFINE)
        self.update_power_display()
        self.update_materials_list()

    def purify_material(self, material):
        if self.power_manager.get_power() < POWER_COST_PER_PURIFY:
            QMessageBox.warning(self, "Insufficient Power", "Not enough power to purify.")
            return

        pure_amount = material.purify(20)
        if pure_amount == 0:
            QMessageBox.information(self, "Purify Failed", "Not enough refined material to purify.")
            return

        self.power_manager.consume_power(POWER_COST_PER_PURIFY)
        self.update_power_display()
        self.update_materials_list()