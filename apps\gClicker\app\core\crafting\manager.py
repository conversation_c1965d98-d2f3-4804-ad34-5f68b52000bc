from typing import Dict, Optional
from apps.gClicker.app.core.crafting.blueprints import ALL_BLUEPRINTS

class CraftingManager:
    def __init__(self, user_data: dict, power_pool: dict):
        """
        :param user_data: User's materials inventory (material_name -> quantity)
        :param power_pool: User's power info, e.g. {"power": int}
        """
        self.user_data = user_data
        self.power_pool = power_pool

    def can_craft(self, item_name: str) -> bool:
        blueprint = ALL_BLUEPRINTS.get(item_name)
        if not blueprint:
            return False
        return blueprint.can_craft(self.user_data, self.power_pool.get("power", 0))

    def craft(self, item_name: str) -> bool:
        """
        Attempt to craft an item by name.
        Returns True if crafting succeeded, False otherwise.
        """
        blueprint = ALL_BLUEPRINTS.get(item_name)
        if not blueprint:
            return False

        if blueprint.consume_resources(self.user_data, self.power_pool):
            # Add crafted items to inventory
            self.user_data[item_name] = self.user_data.get(item_name, 0) + blueprint.output_quantity
            return True

        return False

    def get_all_blueprints(self) -> Dict[str, 'CraftingBlueprint']:
        """Return all available blueprints."""
        return ALL_BLUEPRINTS

    def get_user_inventory(self) -> dict:
        return self.user_data

    def get_power(self) -> int:
        return self.power_pool.get("power", 0)