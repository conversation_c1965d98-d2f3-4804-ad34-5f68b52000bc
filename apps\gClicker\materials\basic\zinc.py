from apps.gClicker.material_base import Material, MaterialTier, MaterialUpgrade

class Zinc(Material):
    def __init__(self):
        super().__init__("Zinc", MaterialTier.BASIC, production_rate=0, unlock_cost=210)
        self.refine_conversion_rate = 3
        self.purify_conversion_rate = 2
    def get_available_upgrades(self) -> list[MaterialUpgrade]:
        return [
            MaterialUpgrade("Zinc Mining I", 52, "production_rate", 1, "Increase zinc production by 1/sec"),
            MaterialUpgrade("Zinc Storage I", 105, "storage_capacity", 50, "Increase zinc storage by 50"),
            MaterialUpgrade("Zinc Refinement I", 210, "unlock_refinement", 1, "Unlock zinc refinement at 1/sec"),
            MaterialUpgrade("Zinc Refinement II", 315, "refine_rate", 1, "Increase zinc refinement by 1/sec"),
            MaterialUpgrade("Zinc Purification I", 420, "unlock_purification", 1, "Unlock zinc purification at 1/sec"),
            MaterialUpgrade("Zinc Purification II", 525, "pure_rate", 1, "Increase zinc purification by 1/sec"),
            MaterialUpgrade("Zinc Mining II", 525, "production_rate", 2, "Increase zinc production by 2/sec"),
        ] 