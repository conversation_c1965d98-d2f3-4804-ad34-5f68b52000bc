from apps.gClicker.app.core.materials.base import BaseMaterial
from apps.gClicker.app.core.materials.tiers import MaterialTier

class AlloyBase(BaseMaterial):
    tier = MaterialTier.ALLOYS  # or create a new tier if needed

    # Alloys are always pure form
    raw_to_refined_ratio = 1
    refined_to_pure_ratio = 1

    def __init__(self, user_data: dict, recipe: dict = None):
        self.name = self.__class__.__name__  # or pass explicitly
        self.recipe = recipe or {}
        self.user_data = user_data
        if self.name not in self.user_data:
            self.user_data[self.name] = {"pure": 0}
        super().__init__(user_data)

    def get_storage(self):
        # Override to only track pure form for alloys
        return self.user_data[self.name]

    def can_craft(self, material_manager) -> bool:
        # Check if material_manager has enough pure materials for recipe
        for mat_name, qty in self.recipe.items():
            mat = material_manager.get_material(mat_name)
            if not mat or mat.get_storage().get("pure", 0) < qty:
                return False
        return True

    def craft(self, material_manager) -> bool:
        if not self.can_craft(material_manager):
            return False
        # Consume materials
        for mat_name, qty in self.recipe.items():
            mat = material_manager.get_material(mat_name)
            mat.get_storage()["pure"] -= qty
        # Add 1 pure unit of alloy
        self.get_storage()["pure"] += 1
        return True