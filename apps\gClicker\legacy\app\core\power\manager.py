class PowerManager:
    def __init__(self, user_data: dict):
        self.user_data = user_data
        # Initialize power state in user_data if missing
        if "power" not in self.user_data:
            self.user_data["power"] = {
                "current": 0,
                "max": 100,
                "regen_rate": 5,  # power units regenerated per tick (optional)
            }

    def get_power(self) -> int:
        return self.user_data["power"]["current"]

    def get_max_power(self) -> int:
        return self.user_data["power"]["max"]

    def set_max_power(self, max_power: int):
        self.user_data["power"]["max"] = max_power
        # Clamp current power to max
        if self.user_data["power"]["current"] > max_power:
            self.user_data["power"]["current"] = max_power

    def add_power(self, amount: int):
        self.user_data["power"]["current"] = min(
            self.user_data["power"]["current"] + amount,
            self.user_data["power"]["max"],
        )

    def consume_power(self, amount: int) -> bool:
        """Consume power if available. Return True if successful, False if insufficient power."""
        if self.user_data["power"]["current"] >= amount:
            self.user_data["power"]["current"] -= amount
            return True
        return False

    def regen_power(self):
        """Regenerate power. Can be called periodically (e.g., game ticks)."""
        self.add_power(self.user_data["power"]["regen_rate"])

    # Optional: Allow setting regen rate dynamically
    def set_regen_rate(self, rate: int):
        self.user_data["power"]["regen_rate"] = rate