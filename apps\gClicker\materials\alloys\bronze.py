from apps.gClicker.material_base import Alloy, MaterialTier, MaterialUpgrade, AlloyRecipe

class Bronze(Alloy):
    def __init__(self):
        # Bronze recipe: 3 raw copper + 1 raw tin -> 1 raw bronze
        recipe = AlloyRecipe(
            name="Bronze",
            ingredients=[
                ("copper", "raw", 3),
                ("tin", "raw", 1)
            ],
            output_amount=1,
            alloying_rate=1
        )
        super().__init__("Bronze", MaterialTier.BASIC, recipe, unlock_cost=500)
        self.refine_conversion_rate = 3
        self.purify_conversion_rate = 2
        
    def get_available_upgrades(self) -> list[MaterialUpgrade]:
        return [
            MaterialUpgrade("Bronze Alloying I", 100, "unlock_alloying", 1, "Unlock bronze alloying at 1/sec"),
            MaterialUpgrade("Bronze Storage I", 150, "storage_capacity", 50, "Increase bronze storage by 50"),
            MaterialUpgrade("Bronze Refinement I", 200, "unlock_refinement", 1, "Unlock bronze refinement at 1/sec"),
            MaterialUpgrade("Bronze Purification I", 300, "unlock_purification", 1, "Unlock bronze purification at 1/sec"),
            
            MaterialUpgrade("Bronze Alloying II", 300, "alloying_rate", 1, "Increase bronze alloying by 1/sec"),
            MaterialUpgrade("Bronze Storage II", 400, "storage_capacity", 100, "Increase bronze storage by 100"),
            MaterialUpgrade("Bronze Refinement II", 500, "refine_rate", 1, "Increase bronze refinement by 1/sec"),
            MaterialUpgrade("Bronze Purification II", 600, "pure_rate", 1, "Increase bronze purification by 1/sec"),
            
            MaterialUpgrade("Bronze Alloying III", 800, "alloying_rate", 2, "Increase bronze alloying by 2/sec"),
            MaterialUpgrade("Bronze Storage III", 1000, "storage_capacity", 250, "Increase bronze storage by 250"),
            MaterialUpgrade("Bronze Refinement III", 1200, "refine_rate", 1, "Increase bronze refinement by 1/sec"),
            MaterialUpgrade("Bronze Purification III", 1500, "pure_rate", 1, "Increase bronze purification by 1/sec"),
        ] 