import os
import json
from user.account import User

USERS_DIR = os.path.join(os.path.dirname(__file__), '..', 'users')

if not os.path.exists(USERS_DIR):
    os.makedirs(USERS_DIR)

def user_folder_path(username):
    return os.path.join(USERS_DIR, username)

def user_data_path(username):
    return os.path.join(user_folder_path(username), 'data.json')

def user_transaction_log_path(username):
    return os.path.join(user_folder_path(username), 'transaction_log.json')

def save_user(user: User):
    folder = user_folder_path(user.username)
    if not os.path.exists(folder):
        os.makedirs(folder)
    with open(user_data_path(user.username), 'w') as f:
        json.dump(user.to_dict(), f, indent=4)

def load_user(username):
    data_path = user_data_path(username)
    if not os.path.isfile(data_path):
        return None
    with open(data_path, 'r') as f:
        data = json.load(f)
    return User(username=None, data=data)

def load_all_users():
    users = {}
    for username in os.listdir(USERS_DIR):
        folder = user_folder_path(username)
        if os.path.isdir(folder):
            user = load_user(username)
            if user:
                users[username] = user
    return users

def delete_user(username):
    folder = user_folder_path(username)
    if os.path.isdir(folder):
        # Delete all files inside user's folder
        for filename in os.listdir(folder):
            os.remove(os.path.join(folder, filename))
        os.rmdir(folder)

# Transaction log functions

def load_transaction_log(username):
    log_path = user_transaction_log_path(username)
    if not os.path.isfile(log_path):
        return []
    with open(log_path, 'r') as f:
        return json.load(f)

def append_transaction(username, transaction):
    """
    transaction is a dict with keys: type, amount, timestamp, description
    """
    log = load_transaction_log(username)
    log.append(transaction)

    with open(user_transaction_log_path(username), 'w') as f:
        json.dump(log, f, indent=4)
