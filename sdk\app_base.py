from PyQt6.QtWidgets import QWidget
from PyQt6.QtCore import pyqtSignal
from PyQt6.QtGui import QIcon
import os


class GNetAppBase(QWidget):
    app_name = "Unamed App"
    app_version = "0.0.0"
    app_description = "No description provided."
    default_icon_path = os.path.join(os.path.dirname(__file__), "assets", "default_icon.png")
    app_icon = QIcon(default_icon_path) if os.path.exists(default_icon_path) else None

    def __init__(self, user_account):
        super().__init__()
        self.user = user_account

    def start(self):
        """Default start method"""
        self.show()

    def stop(self):
        """Call this method when the app wants to close its tab and stop running."""
        parent = self.parent()
        while parent is not None:
            if hasattr(parent, "removeTab"):
                index = parent.indexOf(self)
                if index != -1:
                    parent.removeTab(index)
                return
            parent = parent.parent()