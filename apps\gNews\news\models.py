from dataclasses import dataclass, field
from typing import List
from datetime import datetime

@dataclass
class Comment:
    author: str
    content: str
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class Article:
    id: str
    title: str
    summary: str
    content: str
    author: str
    timestamp: datetime = field(default_factory=datetime.now)
    comments: List[Comment] = field(default_factory=list) 