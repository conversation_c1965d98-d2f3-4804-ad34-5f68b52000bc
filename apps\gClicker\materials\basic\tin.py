from apps.gClicker.material_base import Material, MaterialTier, MaterialUpgrade

class Tin(Material):
    def __init__(self):
        super().__init__("Tin", MaterialTier.BASIC, production_rate=0, unlock_cost=25000)
        self.refine_conversion_rate = 3
        self.purify_conversion_rate = 2
    def get_available_upgrades(self) -> list[MaterialUpgrade]:
        return [
            MaterialUpgrade("Tin Mining I", 8000, "production_rate", 1, "Increase tin production by 1/sec"),
            MaterialUpgrade("Tin Storage I", 6000, "storage_capacity", 50, "Increase tin storage by 50"),
            MaterialUpgrade("Tin Refinement I", 12500, "unlock_refinement", 1, "Unlock tin refinement at 1/sec"),
            MaterialUpgrade("Tin Purification I", (20000, ("copper", "pure", 100)), "unlock_purification", 1, "Unlock tin purification at 1/sec (80 pure copper)"),
            
            MaterialUpgrade("Tin Refinement II", (25000, ("copper", "pure", 50)), "refine_rate", 1, "Increase tin refinement by 1/sec (50 pure copper)"),
            MaterialUpgrade("Tin Purification II", (30000, ("copper", "pure", 200)), "pure_rate", 1, "Increase tin purification by 1/sec (120 pure copper)"),
            MaterialUpgrade("Tin Mining II", (75000, ("copper", "pure", 300)), "production_rate", 2, "Increase tin production by 2/sec (160 pure copper)"),
        ] 