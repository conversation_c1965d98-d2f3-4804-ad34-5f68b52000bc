# logic.py

import random
from apps.gClicker.upgrades import UPGRADES
from PyQt6.QtWidgets import QMessageBox

class ClickerLogic:
    def __init__(self, state):
        self.state = state
        self.ui = None  # to be set by UI

    def handle_click(self):
        base_click = self.state.data["click_value"]
        crit_hit = False

        if random.uniform(0, 100) < self.state.data["crit_chance"]:
            crit_hit = True
            base_click *= self.state.data["crit_multiplier"]

        self.state.data["clicks"] += base_click
        self.state.save()
        self.ui.update_display()

        if crit_hit:
            self.ui.flash_crit(f"Critical Hit! +{int(base_click)} clicks!")

    def add_passive_clicks(self):
        income = self.state.data.get("passive_income", 0)
        if income > 0:
            self.state.data["clicks"] += income
            self.state.save()
            self.ui.update_display()

    def try_purchase_upgrade(self, index):
        upgrade = UPGRADES[index]
        data = self.state.data

        if upgrade["name"] in data["upgrades"]:
            return

        if data["clicks"] < upgrade["cost"]:
            QMessageBox.information(None, "Not Enough Clicks", f"You need {upgrade['cost']} clicks.")
            return

        details = []
        if "power" in upgrade:
            details.append(f"+{upgrade['power']} Click Power")
        if "crit_chance" in upgrade:
            details.append(f"+{upgrade['crit_chance']}% Crit Chance")
        if "crit_multiplier" in upgrade:
            details.append(f"+{upgrade['crit_multiplier']}x Crit Multiplier")
        if "passive_income" in upgrade:
            details.append(f"+{upgrade['passive_income']} clicks/sec")

        confirm = QMessageBox.question(
            None,
            "Confirm Purchase",
            f"Buy {upgrade['name']}?\n\n" + "\n".join(details) + f"\n\nCost: {upgrade['cost']} clicks",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if confirm == QMessageBox.StandardButton.Yes:
            data["clicks"] -= upgrade["cost"]
            if "power" in upgrade:
                data["click_value"] += upgrade["power"]
            if "crit_chance" in upgrade:
                data["crit_chance"] += upgrade["crit_chance"]
            if "crit_multiplier" in upgrade:
                data["crit_multiplier"] *= upgrade["crit_multiplier"]
            if "passive_income" in upgrade:
                data["passive_income"] += upgrade["passive_income"]
            data["upgrades"].append(upgrade["name"])

            self.state.save()
            self.ui.update_display()
            self.ui.populate_upgrades()