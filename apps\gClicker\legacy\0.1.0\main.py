from sdk.app_base import GNetAppBase
from PyQt6.QtWidgets import (
    QVBoxLayout, QLabel, QPushButton, QListWidget, QListWidgetItem, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont, QIcon
import os
import random

UPGRADES = [
    # Click power upgrades
    {"name": "Iron Finger", "power": 1, "cost": 50},
    {"name": "Steel Knuckle", "power": 5, "cost": 200},
    {"name": "Titanium Tap", "power": 10, "cost": 750},
    
    # Crit upgrades
    {"name": "Lucky Tap", "crit_chance": 5, "cost": 500},
    {"name": "Crit Mastery", "crit_multiplier": 2, "cost": 1500},
    
    # Passive income upgrades
    {"name": "Finger Drone", "passive_income": 2, "cost": 100},
    {"name": "Nano Tapper", "passive_income": 5, "cost": 250},
    {"name": "Auto Tapper", "passive_income": 10, "cost": 500},
    {"name": "Giga Click Farm", "passive_income": 15, "cost": 1250},
    {"name": "Quantum Clicker", "passive_income": 50, "cost": 7500},
]

class GClicker(GNetAppBase):
    app_name = "gClicker"
    app_version = "0.2.0"
    app_description = "Click to build power. Upgrade your clicks!"

    app_icon = QIcon(os.path.join(os.path.dirname(__file__), "clicker_icon.png")) \
        if os.path.exists(os.path.join(os.path.dirname(__file__), "clicker_icon.png")) else None

    def __init__(self, user_account):
        super().__init__(user_account)
        self.user_account = user_account
        self.data = self.user_account.get_field("clicker", {
            "clicks": 0,
            "click_value": 1,
            "upgrades": [],
            "crit_chance": 0,
            "crit_multiplier": 2,
            "passive_income": 0,
        })
        self.save()

        self.timer = QTimer()
        self.timer.timeout.connect(self.add_passive_clicks)
        self.timer.start(1000)

        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        title = QLabel("gClicker")
        title.setFont(QFont("Segoe UI", 24))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        self.clicks_label = QLabel()
        self.clicks_label.setFont(QFont("Segoe UI", 16))
        self.clicks_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.clicks_label)

        self.click_power_label = QLabel()
        self.click_power_label.setFont(QFont("Segoe UI", 14))
        self.click_power_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.click_power_label)

        self.effective_power_label = QLabel()
        self.effective_power_label.setFont(QFont("Segoe UI", 14))
        self.effective_power_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.effective_power_label)

        self.passive_income_label = QLabel()
        self.passive_income_label.setFont(QFont("Segoe UI", 14))
        self.passive_income_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.passive_income_label)

        self.crit_chance_label = QLabel()
        self.crit_chance_label.setFont(QFont("Segoe UI", 14))
        self.crit_chance_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.crit_chance_label)

        self.crit_multiplier_label = QLabel()
        self.crit_multiplier_label.setFont(QFont("Segoe UI", 14))
        self.crit_multiplier_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.crit_multiplier_label)

        self.click_button = QPushButton("Click Me!")
        self.click_button.setFont(QFont("Segoe UI", 18))
        self.click_button.clicked.connect(self.handle_click)
        layout.addWidget(self.click_button, alignment=Qt.AlignmentFlag.AlignCenter)

        layout.addWidget(QLabel("Upgrades:"))
        self.upgrade_list = QListWidget()
        layout.addWidget(self.upgrade_list)
        self.upgrade_list.itemClicked.connect(self.try_purchase_upgrade)

        self.populate_upgrades()
        self.update_display()

    def handle_click(self):
        self.click_button.setStyleSheet("background-color: #A0FFA0")
        QTimer.singleShot(150, lambda: self.click_button.setStyleSheet(""))

        base_click = self.data["click_value"]
        crit_hit = False

        if random.uniform(0, 100) < self.data.get("crit_chance", 0):
            crit_hit = True
            base_click *= self.data.get("crit_multiplier", 1)

        self.data["clicks"] += base_click
        self.save()
        self.update_display()

        if crit_hit:
            self.result_message(f"Critical Hit! +{int(base_click)} clicks!")

    def populate_upgrades(self):
        self.upgrade_list.clear()
        for upgrade in UPGRADES:
            if "power" in upgrade:
                text = f"{upgrade['name']} (+{upgrade['power']}) — {upgrade['cost']} clicks"
            elif "crit_chance" in upgrade:
                text = f"{upgrade['name']} (+{upgrade['crit_chance']}% crit chance) — {upgrade['cost']} clicks"
            elif "crit_multiplier" in upgrade:
                text = f"{upgrade['name']} (+{upgrade['crit_multiplier']}x crit multiplier) — {upgrade['cost']} clicks"
            elif "passive_income" in upgrade:
                text = f"{upgrade['name']} (+{upgrade['passive_income']} clicks/sec) — {upgrade['cost']} clicks"
            else:
                text = f"{upgrade['name']} — {upgrade['cost']} clicks"

            item = QListWidgetItem(text)
            if upgrade["name"] in self.data["upgrades"]:
                item.setFlags(Qt.ItemFlag.NoItemFlags)
                item.setText(f"{upgrade['name']} — PURCHASED")
            self.upgrade_list.addItem(item)

    def try_purchase_upgrade(self, item):
        index = self.upgrade_list.row(item)
        upgrade = UPGRADES[index]

        if upgrade["name"] in self.data["upgrades"]:
            return

        if self.data["clicks"] < upgrade["cost"]:
            QMessageBox.information(self, "Not Enough Clicks", f"You need {upgrade['cost']} clicks.")
            return

        details = []
        if "power" in upgrade:
            details.append(f"Click Power Increase: +{upgrade['power']}")
        if "crit_chance" in upgrade:
            details.append(f"Critical Chance Increase: +{upgrade['crit_chance']}%")
        if "crit_multiplier" in upgrade:
            details.append(f"Critical Multiplier Increase: +{upgrade['crit_multiplier']}x")
        if "passive_income" in upgrade:
            details.append(f"Passive Income Increase: +{upgrade['passive_income']} clicks/sec")
        details_text = "\n".join(details)

        confirm = QMessageBox.question(
            self,
            "Confirm Purchase",
            f"Do you want to buy {upgrade['name']}?\n\n{details_text}\nCost: {upgrade['cost']} clicks",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if confirm == QMessageBox.StandardButton.Yes:
            self.data["clicks"] -= upgrade["cost"]

            if "power" in upgrade:
                self.data["click_value"] += upgrade["power"]
            if "crit_chance" in upgrade:
                self.data["crit_chance"] += upgrade["crit_chance"]
            if "crit_multiplier" in upgrade:
                self.data["crit_multiplier"] *= upgrade["crit_multiplier"]
            if "passive_income" in upgrade:
                self.data["passive_income"] += upgrade["passive_income"]

            self.data["upgrades"].append(upgrade["name"])
            self.save()
            self.update_display()
            self.populate_upgrades()

    def update_display(self):
        self.clicks_label.setText(f"Clicks: {int(self.data['clicks'])}")
        self.click_power_label.setText(f"Click Power: {int(self.data['click_value'])}")
        self.passive_income_label.setText(f"Passive Income: {self.data['passive_income']} clicks/sec")
        self.crit_chance_label.setText(f"Critical Chance: {self.data.get('crit_chance', 0)}%")
        self.crit_multiplier_label.setText(f"Critical Multiplier: {self.data.get('crit_multiplier', 1)}x")

        base = self.data['click_value']
        chance = self.data.get('crit_chance', 0)
        mult = self.data.get('crit_multiplier', 1)
        effective = base + (base * (chance / 100) * (mult - 1))
        self.effective_power_label.setText(f"Effective Click Power: {effective:.2f}")

    def save(self):
        self.user_account.set_field("clicker", self.data)

    def result_message(self, message):
        self.setWindowTitle(message)
        QTimer.singleShot(1500, lambda: self.setWindowTitle("gClicker"))

    def add_passive_clicks(self):
        if self.data["passive_income"] > 0:
            self.data["clicks"] += self.data["passive_income"]
            self.save()
            self.update_display()