# Placeholder for gNews main feed UI 
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QLabel, QListWidget, QTextEdit, QHBoxLayout, QPushButton, QLineEdit

class NewsFeedView(QWidget):
    def __init__(self, news_manager, user=None):
        super().__init__()
        self.news_manager = news_manager
        self.user = user
        self._init_ui()

    def _init_ui(self):
        layout = QHBoxLayout()
        self.setLayout(layout)

        # Article list
        self.article_list = QListWidget()
        self.article_list.setFixedWidth(250)
        self.article_list.itemSelectionChanged.connect(self.display_selected_article)
        layout.addWidget(self.article_list)

        # Article display and comments
        right_layout = QVBoxLayout()
        self.article_display = QTextEdit()
        self.article_display.setReadOnly(True)
        right_layout.addWidget(self.article_display)

        # Comments section
        self.comments_label = QLabel("Comments:")
        right_layout.addWidget(self.comments_label)
        self.comments_box = QTextEdit()
        self.comments_box.setReadOnly(True)
        self.comments_box.setFixedHeight(100)
        right_layout.addWidget(self.comments_box)

        # Add comment input
        self.comment_input = QLineEdit()
        self.comment_input.setPlaceholderText("Add a comment...")
        right_layout.addWidget(self.comment_input)
        self.comment_btn = QPushButton("Post Comment")
        self.comment_btn.clicked.connect(self.add_comment)
        right_layout.addWidget(self.comment_btn)

        layout.addLayout(right_layout)

        self.refresh_articles()

    def refresh_articles(self):
        self.article_list.clear()
        for article in self.news_manager.get_articles():
            self.article_list.addItem(f"{article.title} ({article.timestamp.strftime('%Y-%m-%d')})")
        if self.article_list.count() > 0:
            self.article_list.setCurrentRow(0)

    def display_selected_article(self):
        idx = self.article_list.currentRow()
        articles = self.news_manager.get_articles()
        if 0 <= idx < len(articles):
            article = articles[idx]
            self.article_display.setHtml(f"""
                <h2>{article.title}</h2>
                <p><b>By:</b> {article.author} | <b>Date:</b> {article.timestamp.strftime('%Y-%m-%d %H:%M')}</p>
                <p><i>{article.summary}</i></p>
                <hr>
                <div>{article.content}</div>
            """)
            self.display_comments(article)
        else:
            self.article_display.clear()
            self.comments_box.clear()

    def display_comments(self, article):
        if not article.comments:
            self.comments_box.setPlainText("No comments yet.")
        else:
            self.comments_box.setPlainText("\n".join([
                f"[{c.timestamp.strftime('%H:%M')}] {c.author}: {c.content}" for c in article.comments
            ]))

    def add_comment(self):
        idx = self.article_list.currentRow()
        articles = self.news_manager.get_articles()
        if 0 <= idx < len(articles):
            article = articles[idx]
            content = self.comment_input.text().strip()
            if not content or not self.user:
                return
            self.news_manager.add_comment(article.id, self.user.username, content)
            self.comment_input.clear()
            self.display_comments(article) 