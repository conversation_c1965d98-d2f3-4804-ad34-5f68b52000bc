# apps.gClicker.app.ui.upgrades_tab.py

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QMessageBox
)
from PyQt6.QtCore import Qt

from apps.gClicker.app.core.economy.upgrade_store import UpgradeStore


class UpgradesTab(QWidget):
    def __init__(self, user_data: dict, on_upgrade_purchased=None):
        super().__init__()
        self.user_data = user_data
        self.upgrade_store = UpgradeStore(user_data)
        self.on_upgrade_purchased = on_upgrade_purchased

        self.layout = QVBoxLayout(self)

        self.title_label = QLabel("Upgrade Store")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        self.layout.addWidget(self.title_label)

        self.upgrade_list = QListWidget()
        self.layout.addWidget(self.upgrade_list)

        self.summary_label = QLabel()
        self.layout.addWidget(self.summary_label)

        self.refresh_ui()

    def refresh_ui(self):
        self.upgrade_list.clear()

        available = self.upgrade_store.get_available_upgrades()
        owned = self.upgrade_store.get_owned_upgrades()
        currency = self.upgrade_store.get_currency()

        if not available:
            item = QListWidgetItem("All upgrades purchased!")
            self.upgrade_list.addItem(item)
        else:
            for upgrade in available:
                item = QListWidgetItem()
                name = upgrade["name"]
                cost = upgrade["cost"]
                owned_str = " (Owned)" if name in owned else ""
                item.setText(f"{name} - Cost: {cost}{owned_str}")
                item.setData(Qt.ItemDataRole.UserRole, upgrade)
                self.upgrade_list.addItem(item)

        # Connect list item double-click to purchase
        self.upgrade_list.itemDoubleClicked.connect(self.handle_purchase)

        # Show current bonuses
        effects = self.upgrade_store.get_upgrade_effects()
        summary = (
            f"<b>Total Bonuses:</b><br>"
            f"Click Power: +{effects.get('power', 0)}<br>"
            f"Crit Chance: {effects.get('crit_chance', 0)}%<br>"
            f"Crit Multiplier: x{effects.get('crit_multiplier', 1)}<br>"
            f"Passive Income: +{effects.get('passive_income', 0)}"
        )
        self.summary_label.setText(summary)

    def handle_purchase(self, item: QListWidgetItem):
        upgrade = item.data(Qt.ItemDataRole.UserRole)
        name = upgrade["name"]

        if self.upgrade_store.purchase_upgrade(name):
            QMessageBox.information(self, "Upgrade Purchased", f"You bought: {name}")
            if self.on_upgrade_purchased:
                self.on_upgrade_purchased(name)
        else:
            QMessageBox.warning(self, "Purchase Failed", f"Not enough currency for {name}")

        self.refresh_ui()