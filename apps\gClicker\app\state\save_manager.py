from apps.gClicker.app.core.materials.manager import MaterialManager
import user.storage as storage


class SaveManager:
    """
    Central state manager for gClicker. Holds all persistent user data and exposes interfaces to subsystems.
    """

    def __init__(self, user_data: dict, user_account=None):
        """
        Initialize SaveManager with the user-specific data dictionary.
        This is expected to be passed in from a global game save system.
        """
        self.user_data = user_data
        self.user_account = user_account

        # Ensure 'gClicker' namespace exists
        self.user_data.setdefault("gClicker", {})
        self.gclicker_data = self.user_data["gClicker"]

        # Initialize subsystem data structures
        self.gclicker_data.setdefault("materials", {})
        self.gclicker_data.setdefault("power", {})
        self.gclicker_data.setdefault("upgrades", {})

        # Initialize material subsystem
        self.material_manager = MaterialManager(self.gclicker_data["materials"])

    def get_material_manager(self) -> MaterialManager:
        return self.material_manager

    def get_user_data(self) -> dict:
        """
        Returns the full user data dictionary, including all game systems.
        """
        return self.user_data

    def get_gclicker_data(self) -> dict:
        """
        Returns the isolated gClicker game data section.
        """
        return self.gclicker_data

    # Optional: Add hooks for saving/loading if not handled elsewhere
    def serialize(self) -> dict:
        """
        Serialize current state for saving.
        """
        return self.user_data

    def save(self):
        """
        Save the current state to persistent storage.
        """
        if self.user_account:
            self.user_account.save()

    def reset(self):
        """
        Clear out all gClicker-specific data.
        """
        self.user_data["gClicker"] = {}
        self.__init__(self.user_data, self.user_account)


# Standalone function for backward compatibility
def save_user_data(user_data: dict):
    """
    Save user data using the global storage system.
    This is a convenience function for backward compatibility.
    """
    # This is a simplified version - in a real implementation,
    # you'd need access to the user account object
    # For now, this is a placeholder that doesn't break the import
    pass