from apps.gClicker.app.core.materials.manager import MaterialManager


class SaveManager:
    """
    Central state manager for gClicker. Holds all persistent user data and exposes interfaces to subsystems.
    """

    def __init__(self, user_data: dict):
        """
        Initialize SaveManager with the user-specific data dictionary.
        This is expected to be passed in from a global game save system.
        """
        self.user_data = user_data

        # Ensure 'gClicker' namespace exists
        self.user_data.setdefault("gClicker", {})
        self.gclicker_data = self.user_data["gClicker"]

        # Initialize material subsystem
        self.gclicker_data.setdefault("materials", {})
        self.material_manager = MaterialManager(self.gclicker_data["materials"])

        # (Placeholder) Other subsystems like power, upgrades, etc. can go here later
        # Example:
        # self.power_manager = PowerManager(self.gclicker_data.setdefault("power", {}))

    def get_material_manager(self) -> MaterialManager:
        return self.material_manager

    def get_user_data(self) -> dict:
        """
        Returns the full user data dictionary, including all game systems.
        """
        return self.user_data

    def get_gclicker_data(self) -> dict:
        """
        Returns the isolated gClicker game data section.
        """
        return self.gclicker_data

    # Optional: Add hooks for saving/loading if not handled elsewhere
    def serialize(self) -> dict:
        """
        Serialize current state for saving.
        """
        return self.user_data

    def reset(self):
        """
        Clear out all gClicker-specific data.
        """
        self.user_data["gClicker"] = {}
        self.__init__(self.user_data)