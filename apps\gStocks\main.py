from sdk.app_base import GNetAppBase
from PyQt6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QMessageBox, QSpinBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont, QIcon
from matplotlib import pyplot as plt
import os
import random

class gStocks(GNetAppBase):
    app_name = "gStocks"
    app_version = "0.4.3"
    app_description = "A stock market simulator with trends and graphs."
    app_icon = QIcon(os.path.join(os.path.dirname(__file__), "icon.png"))

    TREND_EFFECTS = {
        "stable": {"drift": 0.0, "volatility": 0.01},
        "slight_rise": {"drift": 0.002, "volatility": 0.015},
        "slight_fall": {"drift": -0.002, "volatility": 0.015},
        "normal_rise": {"drift": 0.01, "volatility": 0.02},
        "normal_fall": {"drift": -0.01, "volatility": 0.02},
        "big_jump": {"drift": 0.05, "volatility": 0.03},
        "big_fall": {"drift": -0.05, "volatility": 0.03},
    }

    def __init__(self, user_account):
        super().__init__(user_account)
        self.user = user_account

        self.stocks = {
            "ClickCorp": {"price": 25.0, "trend": "stable", "trend_duration": 0, "history": [25.0]},
            "SmallCo": {"price": 10.0, "trend": "stable", "trend_duration": 0, "history": [10.0]},
            "VoidTech": {"price": 100.0, "trend": "stable", "trend_duration": 0, "history": [100.0]},
            "ToolSquare": {"price": 50.0, "trend": "stable", "trend_duration": 0, "history": [50.0]},
        }

        self.portfolio = self.user.get_field("stocks", {})
        self.balance = self.user.get_field("gBalance")

        self._setup_ui()
        self._start_price_updates()

    def _setup_ui(self):
        self.setWindowTitle(self.app_name)
        layout = QVBoxLayout()
        self.setLayout(layout)

        self.balance_label = QLabel(f"gBalance: {self.balance:.2f}")
        self.balance_label.setFont(QFont("Segoe UI", 16))
        self.balance_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.balance_label)

        layout.addWidget(QLabel("Market Stocks:"))
        self.stock_list = QListWidget()
        self._refresh_stock_list()
        layout.addWidget(self.stock_list)

        self.chart_button = QPushButton("View Chart")
        self.chart_button.clicked.connect(self._show_stock_chart)
        layout.addWidget(self.chart_button)

        control_layout = QHBoxLayout()
        self.qty_spin = QSpinBox()
        self.qty_spin.setMinimum(1)
        self.qty_spin.setMaximum(10000)
        self.qty_spin.setValue(1)
        control_layout.addWidget(QLabel("Quantity:"))
        control_layout.addWidget(self.qty_spin)

        self.buy_button = QPushButton("Buy")
        self.buy_button.clicked.connect(self._buy_stock)
        control_layout.addWidget(self.buy_button)

        self.sell_button = QPushButton("Sell")
        self.sell_button.clicked.connect(self._sell_stock)
        control_layout.addWidget(self.sell_button)

        layout.addLayout(control_layout)

        layout.addWidget(QLabel("Your Portfolio:"))
        self.portfolio_list = QListWidget()
        self._refresh_portfolio_list()
        layout.addWidget(self.portfolio_list)

    def _start_price_updates(self):
        self.price_update_timer = QTimer()
        self.price_update_timer.timeout.connect(self._update_prices)
        self.price_update_timer.start(5000)

    def _choose_new_trend(self):
        trends = [
            ("stable", 10),
            ("slight_rise", 8),
            ("slight_fall", 8),
            ("normal_rise", 5),
            ("normal_fall", 5),
            ("big_jump", 1),
            ("big_fall", 1),
        ]
        trend = random.choices(
            [t[0] for t in trends],
            weights=[t[1] for t in trends],
            k=1
        )[0]
        duration = random.randint(5, 15)
        return trend, duration

    def _update_prices(self):
        for symbol, data in self.stocks.items():
            if data["trend_duration"] <= 0:
                data["trend"], data["trend_duration"] = self._choose_new_trend()

            trend = data["trend"]
            effects = self.TREND_EFFECTS[trend]
            change_percent = random.gauss(effects["drift"], effects["volatility"])
            new_price = max(1, data["price"] * (1 + change_percent))
            data["price"] = round(new_price, 2)
            data["trend_duration"] -= 1

            data.setdefault("history", []).append(new_price)
            if len(data["history"]) > 100:
                data["history"] = data["history"][-100:]

        self._refresh_stock_list()
        self._refresh_portfolio_list()

    def _refresh_stock_list(self):
        self.stock_list.clear()
        for symbol, data in self.stocks.items():
            price = data["price"]
            trend = data["trend"]
            item = QListWidgetItem(f"{symbol}: ${price:.2f} ({trend})")
            self.stock_list.addItem(item)

    def _refresh_portfolio_list(self):
        self.portfolio_list.clear()
        if not self.portfolio:
            self.portfolio_list.addItem("(empty)")
            return

        for symbol, data in self.portfolio.items():
            qty = data.get("quantity", 0)
            avg_price = data.get("average_price", 0)
            current_price = self.stocks.get(symbol, {}).get("price", 0)
            profit = (current_price - avg_price) * qty
            item = QListWidgetItem(
                f"{symbol}: Qty: {qty}, Avg Price: ${avg_price:.2f}, "
                f"Current: ${current_price:.2f}, Profit: ${profit:.2f}"
            )
            self.portfolio_list.addItem(item)

    def _refresh_balance_label(self):
        self.balance_label.setText(f"gBalance: {self.balance:.2f}")

    def _buy_stock(self):
        selected = self.stock_list.currentItem()
        if not selected:
            QMessageBox.warning(self, "Error", "Please select a stock to buy.")
            return

        symbol = selected.text().split(":")[0]
        qty = self.qty_spin.value()
        price = self.stocks[symbol]["price"]
        total_cost = price * qty

        if total_cost > self.balance:
            QMessageBox.warning(self, "Insufficient Funds", f"You need ${total_cost:.2f} but have ${self.balance:.2f}.")
            return

        self.balance -= total_cost
        self.user.set_field("gBalance", self.balance)

        owned = self.portfolio.get(symbol, {"quantity": 0, "average_price": 0.0})
        owned_qty = owned["quantity"]
        owned_avg = owned["average_price"]

        new_qty = owned_qty + qty
        new_avg = ((owned_avg * owned_qty) + (price * qty)) / new_qty

        self.portfolio[symbol] = {"quantity": new_qty, "average_price": new_avg}
        self.user.set_field("stocks", self.portfolio)

        self._refresh_portfolio_list()
        self._refresh_balance_label()

    def _sell_stock(self):
        selected = self.portfolio_list.currentItem()
        if not selected or selected.text() == "(empty)":
            QMessageBox.warning(self, "Error", "Please select a stock to sell.")
            return

        symbol = selected.text().split(":")[0]
        qty = self.qty_spin.value()

        if symbol not in self.portfolio:
            QMessageBox.warning(self, "Error", "You don't own this stock.")
            return

        owned = self.portfolio[symbol]
        owned_qty = owned["quantity"]

        if qty > owned_qty:
            QMessageBox.warning(self, "Error", f"You only own {owned_qty} shares of {symbol}.")
            return

        price = self.stocks[symbol]["price"]
        total_gain = price * qty

        self.balance += total_gain
        self.user.set_field("gBalance", self.balance)

        new_qty = owned_qty - qty
        if new_qty == 0:
            del self.portfolio[symbol]
        else:
            self.portfolio[symbol]["quantity"] = new_qty

        self.user.set_field("stocks", self.portfolio)

        self._refresh_portfolio_list()
        self._refresh_balance_label()

    def _show_stock_chart(self):
        selected = self.stock_list.currentItem()
        if not selected:
            QMessageBox.warning(self, "Error", "Select a stock to view its graph.")
            return

        symbol = selected.text().split(":")[0]
        history = self.stocks[symbol].get("history", [])

        if len(history) < 2:
            QMessageBox.information(self, "No Data", "Not enough data to show chart.")
            return

        plt.figure(figsize=(8, 4))
        plt.plot(history, label=symbol, color="skyblue", linewidth=2)
        plt.title(f"{symbol} Price History")
        plt.xlabel("Time (updates)")
        plt.ylabel("Price")
        plt.grid(True)
        plt.legend()
        plt.tight_layout()
        plt.show()
