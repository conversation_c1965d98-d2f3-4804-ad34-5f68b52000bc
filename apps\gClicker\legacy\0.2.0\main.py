# main.py
from sdk.app_base import G<PERSON>AppBase
from PyQt6.QtCore import QTimer
from PyQt6.QtGui import QIcon
from PyQt6.QtWidgets import QVBoxLayout

import os
from apps.gClicker.logic import <PERSON><PERSON><PERSON><PERSON>og<PERSON>
from apps.gClicker.ui import ClickerUI
from apps.gClicker.state import ClickerState

class GClicker(GNetAppBase):
    app_name = "gClicker"
    app_version = "0.2.0"
    app_description = "Click to build power. Upgrade your clicks!"

    app_icon = QIcon(os.path.join(os.path.dirname(__file__), "clicker_icon.png")) \
        if os.path.exists(os.path.join(os.path.dirname(__file__), "clicker_icon.png")) else None

    def __init__(self, user_account):
        super().__init__(user_account)
        self.user_account = user_account

        self.state = ClickerState(self.user_account)
        self.logic = ClickerLogic(self.state)
        self.ui = ClickerUI(self, self.state, self.logic)

        self.timer = QTimer()
        self.timer.timeout.connect(self.logic.add_passive_clicks)
        self.timer.start(1000)

        layout = QVBoxLayout()
        layout.addLayout(self.ui.build_layout())
        self.setLayout(layout)

        self.ui.update_display()