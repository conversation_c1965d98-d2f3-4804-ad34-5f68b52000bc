from typing import Dict, Optional

class CraftingBlueprint:
    """
    Represents a crafting blueprint (recipe).
    """

    def __init__(
        self,
        name: str,
        required_materials: Dict[str, int],
        power_cost: Optional[int] = 0,
        crafting_time_seconds: Optional[float] = 0.0,
        output_quantity: int = 1,
    ):
        """
        :param name: The name of the crafted item
        :param required_materials: Dict of material_name: quantity needed
        :param power_cost: Power cost to craft (default 0)
        :param crafting_time_seconds: Time in seconds to craft (default 0)
        :param output_quantity: How many units produced per craft (default 1)
        """
        self.name = name
        self.required_materials = required_materials
        self.power_cost = power_cost
        self.crafting_time_seconds = crafting_time_seconds
        self.output_quantity = output_quantity

    def can_craft(self, inventory: Dict[str, int], available_power: int) -> bool:
        """
        Check if crafting is possible given inventory and power.
        """
        if available_power < self.power_cost:
            return False
        for mat, qty in self.required_materials.items():
            if inventory.get(mat, 0) < qty:
                return False
        return True

    def consume_resources(self, inventory: Dict[str, int], power_pool: Dict[str, int]) -> bool:
        """
        Consume required materials and power from inventory and power pool.
        Returns True if successful, False if insufficient resources.
        """
        if not self.can_craft(inventory, power_pool.get("power", 0)):
            return False

        # Consume materials
        for mat, qty in self.required_materials.items():
            inventory[mat] -= qty

        # Consume power
        power_pool["power"] -= self.power_cost

        return True


# Define all available blueprints
ALL_BLUEPRINTS = {
    # Example blueprints - you can modify these based on your game's needs
    "Bronze": CraftingBlueprint(
        name="Bronze",
        required_materials={"Copper": 3, "Tin": 1},
        power_cost=5,
        crafting_time_seconds=2.0,
        output_quantity=1
    ),
    "Steel": CraftingBlueprint(
        name="Steel",
        required_materials={"Iron": 2, "Coal": 1},
        power_cost=10,
        crafting_time_seconds=3.0,
        output_quantity=1
    ),
    "Basic Tool": CraftingBlueprint(
        name="Basic Tool",
        required_materials={"Wood": 2, "Stone": 1},
        power_cost=2,
        crafting_time_seconds=1.0,
        output_quantity=1
    ),
}
