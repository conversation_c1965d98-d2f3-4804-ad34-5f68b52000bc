from enum import Enum


class MaterialTier(Enum):
    BASIC = "Basic"
    MODERATE = "Moderate"
    ADVANCED = "Advanced"
    INSANE = "Insane"
    UNREAL = "Unreal"
    ALLOYS = "Alloys"

    def __str__(self):
        return self.value

    @staticmethod
    def all_tiers():
        return list(MaterialTier)

    @staticmethod
    def progression_order():
        """Returns tiers in progression order, from lowest to highest."""
        return [
            MaterialTier.BASIC,
            MaterialTier.MODERATE,
            MaterialTier.ADVANCED,
            MaterialTier.INSANE,
            MaterialTier.UNREAL,
        ]