# gnet_style.py

GNET_STYLESHEET = """
/* General */
QWidget {
    background-color: #121212;
    color: #E0E0E0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 12pt;
}

/* Buttons */
GNetButton {
    background-color: #3A86FF;
    border-radius: 6px;
    padding: 10px 20px;
    color: white;
    font-weight: bold;
}
GNetButton:hover {
    background-color: #265DAB;
}
GNetButton:pressed {
    background-color: #1B3A6F;
}

/* Input Fields */
GNetInput {
    background-color: #222222;
    border: 2px solid #1A3A6F;
    border-radius: 6px;
    padding: 8px;
    color: white;
}
GNetInput:focus {
    border-color: #60AFFF;
}

/* Labels */
QLabel {
    font-weight: 600;
}

/* Scrollbars, menus, and others can be added here */
"""