# ui.py

from PyQt6.QtWidgets import (
    QVBoxLayout, QLabel, QPushButton, QListWidget, QListWidgetItem, QWidget
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont
from apps.gClicker.upgrades import UPGRADES

class ClickerUI:
    def __init__(self, app: QWidget, state, logic):
        self.app = app
        self.state = state
        self.logic = logic
        logic.ui = self  # handshake back to logic

        self.clicks_label = QLabel()
        self.click_power_label = QLabel()
        self.effective_power_label = QLabel()
        self.passive_income_label = QLabel()
        self.crit_chance_label = QLabel()
        self.crit_multiplier_label = QLabel()
        self.click_button = QPushButton("Click Me!")
        self.upgrade_list = QListWidget()

    def build_layout(self):
        layout = QVBoxLayout()

        title = QLabel("gClicker")
        title.setFont(QFont("Segoe UI", 24))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        for lbl in [self.clicks_label, self.click_power_label, self.effective_power_label,
                    self.passive_income_label, self.crit_chance_label, self.crit_multiplier_label]:
            lbl.setFont(QFont("Segoe UI", 14))
            lbl.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(lbl)

        self.click_button.setFont(QFont("Segoe UI", 18))
        self.click_button.clicked.connect(self.logic.handle_click)
        layout.addWidget(self.click_button, alignment=Qt.AlignmentFlag.AlignCenter)

        layout.addWidget(QLabel("Upgrades:"))
        layout.addWidget(self.upgrade_list)
        self.upgrade_list.itemClicked.connect(lambda item: self.logic.try_purchase_upgrade(self.upgrade_list.row(item)))

        self.populate_upgrades()
        return layout

    def populate_upgrades(self):
        self.upgrade_list.clear()
        for upgrade in UPGRADES:
            if "power" in upgrade:
                desc = f"(+{upgrade['power']})"
            elif "crit_chance" in upgrade:
                desc = f"(+{upgrade['crit_chance']}% crit)"
            elif "crit_multiplier" in upgrade:
                desc = f"(+{upgrade['crit_multiplier']}x crit)"
            elif "passive_income" in upgrade:
                desc = f"(+{upgrade['passive_income']} clicks/sec)"
            else:
                desc = ""
            text = f"{upgrade['name']} {desc} — {upgrade['cost']} clicks"
            item = QListWidgetItem(text)
            if upgrade["name"] in self.state.data["upgrades"]:
                item.setFlags(Qt.ItemFlag.NoItemFlags)
                item.setText(f"{upgrade['name']} — PURCHASED")
            self.upgrade_list.addItem(item)

    def update_display(self):
        data = self.state.data
        self.clicks_label.setText(f"Clicks: {int(data['clicks'])}")
        self.click_power_label.setText(f"Click Power: {int(data['click_value'])}")
        self.passive_income_label.setText(f"Passive Income: {data['passive_income']} clicks/sec")
        self.crit_chance_label.setText(f"Critical Chance: {data.get('crit_chance', 0)}%")
        self.crit_multiplier_label.setText(f"Critical Multiplier: {data.get('crit_multiplier', 1)}x")

        base = data['click_value']
        chance = data['crit_chance']
        mult = data['crit_multiplier']
        effective = base + (base * (chance / 100) * (mult - 1))
        self.effective_power_label.setText(f"Effective Click Power: {effective:.2f}")

    def flash_crit(self, msg):
        self.app.setWindowTitle(msg)
        QTimer.singleShot(1500, lambda: self.app.setWindowTitle("gClicker"))
        self.click_button.setStyleSheet("background-color: #A0FFA0")
        QTimer.singleShot(150, lambda: self.click_button.setStyleSheet(""))