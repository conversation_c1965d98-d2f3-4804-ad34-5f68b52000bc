import user.storage as storage
from user.account import User

class UserManager:
    def __init__(self):
        # Load users from disk on startup
        self.users = storage.load_all_users()
        self.current_user = None

    def create_user(self, username, password):
        if username in self.users:
            raise ValueError(f"User '{username}' already exists.")

        user = User(username, password)
        self.users[username] = user
        user.save()  # save to disk immediately
        return user

    def delete_user(self, username):
        if username not in self.users:
            raise ValueError(f"User '{username}' does not exist.")

        storage.delete_user(username)  # remove file
        del self.users[username]

        if self.current_user and self.current_user.username == username:
            self.current_user = None

    def get_user(self, username):
        return self.users.get(username)

    def list_users(self):
        return list(self.users.keys())

    def login(self, username, password):
        user = self.get_user(username)
        if not user:
            raise ValueError("User not found.")

        if not user.check_password(password):
            raise ValueError("Incorrect password.")

        self.current_user = user
        return user

    def logout(self):
        self.current_user = None

    def is_logged_in(self):
        return self.current_user is not None
