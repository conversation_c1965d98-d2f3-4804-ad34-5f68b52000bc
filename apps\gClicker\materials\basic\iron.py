from apps.gClicker.material_base import Material, MaterialTier, MaterialUpgrade

class Iron(Material):
    def __init__(self):
        super().__init__("Iron", MaterialTier.BASIC, production_rate=0, unlock_cost=220)
        self.refine_conversion_rate = 3
        self.purify_conversion_rate = 2
    def get_available_upgrades(self) -> list[MaterialUpgrade]:
        return [
            MaterialUpgrade("Iron Mining I", 55, "production_rate", 1, "Increase iron production by 1/sec"),
            MaterialUpgrade("Iron Storage I", 110, "storage_capacity", 50, "Increase iron storage by 50"),
            MaterialUpgrade("Iron Refinement I", 220, "unlock_refinement", 1, "Unlock iron refinement at 1/sec"),
            MaterialUpgrade("Iron Refinement II", 330, "refine_rate", 1, "Increase iron refinement by 1/sec"),
            MaterialUpgrade("Iron Purification I", 440, "unlock_purification", 1, "Unlock iron purification at 1/sec"),
            MaterialUpgrade("Iron Purification II", 550, "pure_rate", 1, "Increase iron purification by 1/sec"),
            MaterialUpgrade("Iron Mining II", 550, "production_rate", 2, "Increase iron production by 2/sec"),
        ] 