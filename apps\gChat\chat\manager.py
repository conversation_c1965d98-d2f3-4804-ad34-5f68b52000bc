from .models import Message, Channel, UserPresence
from .storage import ChatStorage
from typing import List, Dict

class ChatManager:
    def __init__(self):
        self.channels: Dict[str, Channel] = {}
        self.presence: Dict[str, UserPresence] = {}
        self.storage = ChatStorage()
        self.load_channels()

    def load_channels(self):
        channels = self.storage.load_channels()
        for channel in channels:
            self.channels[channel.name] = channel

    def send_message(self, channel_name: str, sender: str, content: str):
        if channel_name not in self.channels:
            self.channels[channel_name] = Channel(name=channel_name)
        message = Message(sender=sender, content=content)
        self.channels[channel_name].messages.append(message)
        self.storage.save_channel(self.channels[channel_name])

    def get_messages(self, channel_name: str) -> List[Message]:
        if channel_name in self.channels:
            return self.channels[channel_name].messages
        return []

    def set_user_presence(self, username: str, online: bool):
        self.presence[username] = UserPresence(username=username, online=online)

    def get_user_presence(self, username: str) -> bool:
        return self.presence.get(username, UserPresence(username, False)).online 