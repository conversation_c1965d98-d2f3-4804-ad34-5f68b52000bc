from dataclasses import dataclass
from typing import Dict, <PERSON>, Tuple, List
from enum import Enum

class MaterialTier(Enum):
    BASIC = "basic"
    MODERATE = "moderate"
    ADVANCED = "advanced"
    INSANE = "insane"

@dataclass
class MaterialUpgrade:
    name: str
    cost: Union[int, Tuple[int, Tuple[str, str, int]]]
    effect_type: str  # "production_speed", "storage_capacity", "unlock_refinement"
    value: float
    description: str

@dataclass
class AlloyRecipe:
    """Defines a recipe for creating an alloy"""
    name: str
    ingredients: List[Tuple[str, str, int]]  # (material_name, state, amount)
    output_amount: int = 1
    alloying_rate: int = 1  # How much can be alloyed per tick

class Material:
    def __init__(self, name: str, tier: MaterialTier, production_rate: int = 1, unlock_cost: int = 10):
        self.name = name
        self.tier = tier
        self.production_rate = int(production_rate)
        self.unlock_cost = unlock_cost
        self.unlocked = False
        self.refine_rate = 1
        self.pure_rate = 1
        self.refine_conversion_rate = 3  # New: 3 raw -> 1 refined
        self.purify_conversion_rate = 2  # New: 2 refined -> 1 pure
        self.can_purify = False
        self.storage = {
            "raw": 0,
            "refined": 0,
            "pure": 0
        }
        self.upgrades_owned = []
        self.storage_capacity = 100
        self.can_refine = False
        self.last_production_time = 0
        self.auto_refine_enabled = True
        self.auto_purify_enabled = True
        
    def get_production_rate(self) -> int:
        return self.production_rate
    
    def can_produce(self) -> bool:
        """Check if material can be produced (storage not full)"""
        return self.unlocked and self.storage["raw"] < self.storage_capacity
    
    def produce(self, amount: int = None) -> int:
        """Produce material. Returns amount actually produced."""
        if amount is None:
            amount = self.get_production_rate()
        
        if not self.can_produce():
            return 0
        
        space_available = self.storage_capacity - self.storage["raw"]
        actual_production = min(amount, space_available)
        
        self.storage["raw"] += actual_production
        return actual_production
    
    def refine(self, amount: int = None) -> int:
        if not self.can_refine:
            return 0
        max_possible = self.storage["raw"] // self.refine_conversion_rate
        max_tick = self.refine_rate
        # Limit by available refined storage
        available_space = self.storage_capacity - self.storage["refined"]
        conversions = min(max_possible, max_tick, available_space)
        if conversions <= 0:
            return 0
        self.storage["raw"] -= conversions * self.refine_conversion_rate
        self.storage["refined"] += conversions
        return conversions
    
    def purify(self, amount: int = None) -> int:
        if not self.can_purify:
            return 0
        max_possible = self.storage["refined"] // self.purify_conversion_rate
        max_tick = self.pure_rate
        # Limit by available pure storage
        available_space = self.storage_capacity - self.storage["pure"]
        conversions = min(max_possible, max_tick, available_space)
        if conversions <= 0:
            return 0
        self.storage["refined"] -= conversions * self.purify_conversion_rate
        self.storage["pure"] += conversions
        return conversions
    
    def get_storage_usage(self) -> Dict[str, int]:
        """Get storage usage percentages"""
        return {
            "raw": int((self.storage["raw"] / self.storage_capacity) * 100),
            "refined": int((self.storage["refined"] / self.storage_capacity) * 100),
            "pure": int((self.storage["pure"] / self.storage_capacity) * 100)
        }
    
    def can_buy_upgrade(self, upgrade: MaterialUpgrade, currency: int, material_manager=None) -> bool:
        """Check if upgrade can be purchased (currency and material if needed)"""
        if upgrade.name in self.upgrades_owned:
            return False
        if isinstance(upgrade.cost, int):
            return currency >= upgrade.cost
        elif isinstance(upgrade.cost, tuple) and material_manager:
            cost_currency, (mat, state, amt) = upgrade.cost
            return currency >= cost_currency and material_manager.has_material(mat, state, amt)
        return False
    
    def buy_upgrade(self, upgrade: MaterialUpgrade, currency: int, material_manager=None) -> bool:
        """Purchase an upgrade. Returns True if successful."""
        if not self.can_buy_upgrade(upgrade, currency, material_manager):
            return False
        # Deduct costs
        if isinstance(upgrade.cost, int):
            pass  # Only currency, handled by caller
        elif isinstance(upgrade.cost, tuple) and material_manager:
            _, (mat, state, amt) = upgrade.cost
            material_manager.use_material(mat, state, amt)
        self.upgrades_owned.append(upgrade.name)
        if upgrade.effect_type == "production_rate":
            self.production_rate += int(upgrade.value)
        elif upgrade.effect_type == "storage_capacity":
            self.storage_capacity += int(upgrade.value)
        elif upgrade.effect_type == "unlock_refinement":
            self.can_refine = True
        elif upgrade.effect_type == "refine_rate":
            self.refine_rate += int(upgrade.value)
        elif upgrade.effect_type == "unlock_purification":
            self.can_purify = True
        elif upgrade.effect_type == "pure_rate":
            self.pure_rate += int(upgrade.value)
        elif upgrade.effect_type == "unlock_alloying":
            self.can_alloy = True
        elif upgrade.effect_type == "alloying_rate":
            self.alloying_rate += int(upgrade.value)
        return True
    
    def get_available_upgrades(self) -> list[MaterialUpgrade]:
        """Get list of available upgrades for this material"""
        # This will be overridden by specific materials
        return []
    
    def to_dict(self) -> dict:
        """Convert material state to dictionary for saving"""
        return {
            "name": self.name,
            "tier": self.tier.value,
            "storage": self.storage.copy(),
            "upgrades_owned": self.upgrades_owned.copy(),
            "production_rate": self.production_rate,
            "storage_capacity": self.storage_capacity,
            "can_refine": self.can_refine,
            "refine_rate": self.refine_rate,
            "can_purify": self.can_purify,
            "pure_rate": self.pure_rate,
            "refine_conversion_rate": self.refine_conversion_rate,
            "purify_conversion_rate": self.purify_conversion_rate,
            "unlocked": self.unlocked,
            "auto_refine_enabled": self.auto_refine_enabled,
            "auto_purify_enabled": self.auto_purify_enabled
        }
    
    def from_dict(self, data: dict):
        """Load material state from dictionary"""
        self.storage = data.get("storage", {"raw": 0, "refined": 0, "pure": 0})
        # Ensure all keys exist
        if "raw" not in self.storage:
            self.storage["raw"] = 0
        if "refined" not in self.storage:
            self.storage["refined"] = 0
        if "pure" not in self.storage:
            self.storage["pure"] = 0
        self.upgrades_owned = data.get("upgrades_owned", [])
        self.production_rate = data.get("production_rate", 1)
        self.storage_capacity = data.get("storage_capacity", 100)
        self.can_refine = data.get("can_refine", False)
        self.refine_rate = data.get("refine_rate", 1)
        self.can_purify = data.get("can_purify", False)
        self.pure_rate = data.get("pure_rate", 1)
        self.refine_conversion_rate = data.get("refine_conversion_rate", 3)
        self.purify_conversion_rate = data.get("purify_conversion_rate", 2)
        self.unlocked = data.get("unlocked", False)
        self.auto_refine_enabled = data.get("auto_refine_enabled", True)
        self.auto_purify_enabled = data.get("auto_purify_enabled", True)

class Alloy(Material):
    """Base class for alloy materials that are created by combining other materials"""
    
    def __init__(self, name: str, tier: MaterialTier, recipe: AlloyRecipe, unlock_cost: int = 10):
        super().__init__(name, tier, production_rate=0, unlock_cost=unlock_cost)
        self.recipe = recipe
        self.alloying_rate = recipe.alloying_rate
        self.can_alloy = False
        self.auto_alloy_enabled = True
        
    def can_alloy_material(self, material_manager) -> bool:
        """Check if we can alloy this material (have ingredients and space)"""
        if not self.can_alloy or not self.unlocked:
            return False
            
        # Check if we have space for output
        if self.storage["raw"] >= self.storage_capacity:
            return False
            
        # Check if we have all required ingredients
        for material_name, state, amount in self.recipe.ingredients:
            if not material_manager.has_material(material_name, state, amount):
                return False
                
        return True
    
    def alloy(self, material_manager) -> int:
        """Attempt to alloy this material. Returns amount actually alloyed."""
        if not self.can_alloy_material(material_manager):
            return 0
            
        # Calculate how much we can alloy based on available ingredients
        max_alloyable = float('inf')
        for material_name, state, amount in self.recipe.ingredients:
            available = material_manager.get_material_amount(material_name, state)
            max_alloyable = min(max_alloyable, available // amount)
            
        # Limit by alloying rate and storage space
        max_alloyable = min(max_alloyable, self.alloying_rate)
        space_available = self.storage_capacity - self.storage["raw"]
        max_alloyable = min(max_alloyable, space_available // self.recipe.output_amount)
        
        if max_alloyable <= 0:
            return 0
            
        # Consume ingredients and produce alloy
        for material_name, state, amount in self.recipe.ingredients:
            material_manager.use_material(material_name, state, amount * max_alloyable)
            
        self.storage["raw"] += max_alloyable * self.recipe.output_amount
        return max_alloyable
    
    def to_dict(self) -> dict:
        """Convert alloy state to dictionary for saving"""
        base_dict = super().to_dict()
        base_dict.update({
            "can_alloy": self.can_alloy,
            "alloying_rate": self.alloying_rate,
            "auto_alloy_enabled": self.auto_alloy_enabled
        })
        return base_dict
    
    def from_dict(self, data: dict):
        """Load alloy state from dictionary"""
        super().from_dict(data)
        self.can_alloy = data.get("can_alloy", False)
        self.alloying_rate = data.get("alloying_rate", 1)
        self.auto_alloy_enabled = data.get("auto_alloy_enabled", True)

class Copper(Material):
    def __init__(self):
        super().__init__("Copper", MaterialTier.BASIC, production_rate=0, unlock_cost=200)
        self.refine_conversion_rate = 3
        self.purify_conversion_rate = 2
        
    def get_available_upgrades(self) -> list[MaterialUpgrade]:
        return [
            MaterialUpgrade("Copper Mining I", 50, "production_rate", 1, "Increase copper production by 1/sec"),
            MaterialUpgrade("Copper Storage I", 100, "storage_capacity", 50, "Increase copper storage by 50"),
            MaterialUpgrade("Copper Refinement I", 200, "unlock_refinement", 1, "Unlock copper refinement at 1/sec"),
            MaterialUpgrade("Copper Refinement II", 300, "refine_rate", 1, "Increase copper refinement by 1/sec"),
            MaterialUpgrade("Copper Purification I", 400, "unlock_purification", 1, "Unlock copper purification at 1/sec"),
            MaterialUpgrade("Copper Purification II", 500, "pure_rate", 1, "Increase copper purification by 1/sec"),
            MaterialUpgrade("Copper Mining II", 500, "production_rate", 2, "Increase copper production by 2/sec"),
        ] 