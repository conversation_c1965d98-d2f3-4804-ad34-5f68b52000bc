from typing import Dict, Optional
from apps.gClicker.app.core.materials.tiers import MaterialTier
from apps.gClicker.app.core.materials.base import MaterialProcessStage


class MaterialInventory:
    def __init__(self, user_data: dict, material_manager=None):
        self.data: Dict[str, Dict[str, int]] = user_data.setdefault("materials", {})
        self.material_manager = material_manager  # Optional for tier-based queries

    def get_amount(self, name: str, stage: MaterialProcessStage) -> int:
        return self.data.get(name, {}).get(stage.value, 0)

    def set_amount(self, name: str, stage: MaterialProcessStage, amount: int):
        self._ensure_entry(name)
        self.data[name][stage.value] = max(0, amount)

    def add(self, name: str, stage: MaterialProcessStage, amount: int):
        self._ensure_entry(name)
        self.data[name][stage.value] += amount

    def remove(self, name: str, stage: MaterialProcessStage, amount: int) -> bool:
        current = self.get_amount(name, stage)
        if current < amount:
            return False
        self.data[name][stage.value] -= amount
        return True

    def get_full_inventory(self) -> Dict[str, Dict[str, int]]:
        return {name: stages.copy() for name, stages in self.data.items()}

    def get_by_tier(self, tier: MaterialTier) -> Dict[str, Dict[str, int]]:
        if self.material_manager is None:
            raise RuntimeError("MaterialManager is required for tier-based queries")

        return {
            name: self.data[name]
            for name in self.data
            if self.material_manager.get_material(name).tier == tier
        }

    def _ensure_entry(self, name: str):
        if name not in self.data:
            self.data[name] = {stage.value: 0 for stage in MaterialProcessStage}