from PyQt6.QtWidgets import QPushButton, QLineEdit
from PyQt6.QtGui import QFont

class G<PERSON>Button(QPushButton):
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("GNetButton")
        self.setFont(QFont("Segoe UI", 12, weight=75))  # Bold 12pt font

class GNetInput(QLineEdit):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("GNetInput")
        self.setFont(QFont("Segoe UI", 11))
        self.setFixedHeight(36)
