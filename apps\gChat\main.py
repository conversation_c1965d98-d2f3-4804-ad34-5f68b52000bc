# gChat main entry point
from sdk.app_base import GNetAppBase
from PyQt6.QtWidgets import <PERSON><PERSON>ox<PERSON><PERSON>out, QLabel, QHBoxLayout, QListWidget, QTextEdit, QPushButton
from PyQt6.QtGui import <PERSON><PERSON>ont, QIcon
from PyQt6.QtCore import Qt
import os
from .chat.manager import ChatManager

class GChat(GNetAppBase):
    app_name = "gChat"
    app_version = "1.0.0"
    app_description = "A simple, gNet-branded chat app for public channels."
    app_icon = QIcon(os.path.join(os.path.dirname(__file__), "chat_icon.png")) if os.path.exists(os.path.join(os.path.dirname(__file__), "chat_icon.png")) else None

    def __init__(self, user_account):
        super().__init__(user_account)
        self.chat_manager = ChatManager()
        self.username = user_account.username
        self._init_ui()

    def _init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        title = QLabel("gChat")
        title.setFont(QFont("Segoe UI", 24))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Channel list and chat area
        main_area = QHBoxLayout()
        self.channel_list = QListWidget()
        self.channel_list.setFixedWidth(150)
        self.channel_list.addItems(self.chat_manager.channels.keys() or ["general"])
        self.channel_list.currentTextChanged.connect(self.load_channel)
        main_area.addWidget(self.channel_list)

        chat_area = QVBoxLayout()
        self.message_list = QTextEdit()
        self.message_list.setReadOnly(True)
        chat_area.addWidget(self.message_list)

        input_area = QHBoxLayout()
        self.input_box = QTextEdit()
        self.input_box.setFixedHeight(40)
        input_area.addWidget(self.input_box)
        self.send_button = QPushButton("Send")
        self.send_button.clicked.connect(self.send_message)
        input_area.addWidget(self.send_button)
        chat_area.addLayout(input_area)

        main_area.addLayout(chat_area)
        layout.addLayout(main_area)

        self.current_channel = self.channel_list.currentItem().text() if self.channel_list.currentItem() else "general"
        self.load_channel(self.current_channel)

    def load_channel(self, channel_name):
        self.current_channel = channel_name
        messages = self.chat_manager.get_messages(channel_name)
        self.message_list.clear()
        for msg in messages:
            self.message_list.append(f"[{msg.timestamp.strftime('%H:%M')}] <b>{msg.sender}</b>: {msg.content}")

    def send_message(self):
        content = self.input_box.toPlainText().strip()
        if not content:
            return
        self.chat_manager.send_message(self.current_channel, self.username, content)
        self.input_box.clear()
        self.load_channel(self.current_channel) 