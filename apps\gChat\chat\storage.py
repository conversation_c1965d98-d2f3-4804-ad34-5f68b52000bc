import os
import json
from typing import List
from .models import Channel, Message
from datetime import datetime

CHAT_DATA_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../data'))

class ChatStorage:
    def __init__(self):
        os.makedirs(CHAT_DATA_DIR, exist_ok=True)

    def channel_path(self, channel_name: str) -> str:
        return os.path.join(CHAT_DATA_DIR, f"{channel_name}.json")

    def save_channel(self, channel: Channel):
        path = self.channel_path(channel.name)
        with open(path, 'w', encoding='utf-8') as f:
            json.dump({
                'name': channel.name,
                'messages': [
                    {'sender': m.sender, 'content': m.content, 'timestamp': m.timestamp.isoformat()}
                    for m in channel.messages
                ]
            }, f, indent=2)

    def load_channels(self) -> List[Channel]:
        channels = []
        for filename in os.listdir(CHAT_DATA_DIR):
            if filename.endswith('.json'):
                with open(os.path.join(CHAT_DATA_DIR, filename), 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    messages = [
                        Message(sender=m['sender'], content=m['content'], timestamp=datetime.fromisoformat(m['timestamp']))
                        for m in data.get('messages', [])
                    ]
                    channels.append(Channel(name=data['name'], messages=messages))
        return channels 