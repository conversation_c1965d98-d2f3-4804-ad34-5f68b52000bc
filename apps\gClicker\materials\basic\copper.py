from apps.gClicker.material_base import Material, MaterialTier, MaterialUpgrade

class Copper(Material):
    def __init__(self):
        super().__init__("Copper", MaterialTier.BASIC, production_rate=0, unlock_cost=200)
        self.refine_conversion_rate = 3
        self.purify_conversion_rate = 2
    def get_available_upgrades(self) -> list[MaterialUpgrade]:
        return [
            MaterialUpgrade("Copper Mining I", 50, "production_rate", 1, "Increase copper production by 1/sec"),
            MaterialUpgrade("Copper Storage I", 100, "storage_capacity", 50, "Increase copper storage by 50"),
            MaterialUpgrade("Copper Refinement I", 200, "unlock_refinement", 1, "Unlock copper refinement at 1/sec"),
            MaterialUpgrade("Copper Purification I", 400, "unlock_purification", 1, "Unlock copper purification at 1/sec"),
            
            MaterialUpgrade("Copper Storage II", 200, "storage_capacity", 100, "Increase copper storage by 100"),
            MaterialUpgrade("Copper Refinement II", 300, "refine_rate", 1, "Increase copper refinement by 1/sec"),
            MaterialUpgrade("Copper Purification II", 500, "pure_rate", 1, "Increase copper purification by 1/sec"),
            MaterialUpgrade("Copper Mining II", 500, "production_rate", 2, "Increase copper production by 2/sec"),
    
            MaterialUpgrade("Copper Storage III", 400, "storage_capacity", 250, "Increase copper storage by 250"),
            MaterialUpgrade("Copper Mining III", 1000, "production_rate", 3, "Increase copper production by 3/sec"),
            MaterialUpgrade("Copper Refinement III", 750, "refine_rate", 1, "Increase copper refinement by 1/sec"),
            MaterialUpgrade("Copper Purification III", 800, "pure_rate", 1, "Increase copper purification by 1/sec"),
    
            MaterialUpgrade("Copper Storage IV", 800, "storage_capacity", 300, "Increase copper storage by 300"),
            # T4 Upgrades
            MaterialUpgrade("Copper Mining IV", (20000, ("tin", "pure", 10)), "production_rate", 5, "Increase copper production by 5/sec (5 pure tin)"),
            MaterialUpgrade("Copper Refinement IV", (30000, ("tin", "pure", 20)), "refine_rate", 2, "Increase copper refinement by 2/sec (10 pure tin)"),
            MaterialUpgrade("Copper Purification IV", (40000, ("tin", "pure", 30)), "pure_rate", 2, "Increase copper purification by 2/sec (15 pure tin)"),
        ]