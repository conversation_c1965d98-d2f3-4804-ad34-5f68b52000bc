from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QLineEdit, QTextEdit, QPushButton, QMessageBox

class ArticleEditor(QWidget):
    def __init__(self, news_manager, author, on_article_added=None):
        super().__init__()
        self.news_manager = news_manager
        self.author = author
        self.on_article_added = on_article_added
        self._init_ui()

    def _init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        layout.addWidget(QLabel("Title:"))
        self.title_input = QLineEdit()
        layout.addWidget(self.title_input)

        layout.addWidget(QLabel("Summary:"))
        self.summary_input = QLineEdit()
        layout.addWidget(self.summary_input)

        layout.addWidget(QLabel("Content:"))
        self.content_input = QTextEdit()
        layout.addWidget(self.content_input)

        self.submit_btn = QPushButton("Post Article")
        self.submit_btn.clicked.connect(self.submit_article)
        layout.addWidget(self.submit_btn)

    def submit_article(self):
        title = self.title_input.text().strip()
        summary = self.summary_input.text().strip()
        content = self.content_input.toPlainText().strip()
        if not title or not summary or not content:
            QMessageBox.warning(self, "Missing Fields", "Please fill in all fields.")
            return
        self.news_manager.add_article(title, summary, content, self.author)
        QMessageBox.information(self, "Success", "Article posted!")
        self.title_input.clear()
        self.summary_input.clear()
        self.content_input.clear()
        if self.on_article_added:
            self.on_article_added() 