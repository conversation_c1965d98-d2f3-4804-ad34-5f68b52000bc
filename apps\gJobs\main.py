from sdk.app_base import GNetAppBase
from PyQt6.QtWidgets import (
    QVBoxLayout, QLabel, QPushButton, QLineEdit,
    QMessageBox, QHBoxLayout
)
from PyQt6.QtGui import QFont, QIcon
from PyQt6.QtCore import Qt, QTimer
import random
import time
import os

class gJobs(GNetAppBase):
    app_name = "gJobs"
    app_version = "1.1.5"
    app_description = "Earn gBalance by completing mini effort-based tasks."
    # Safe icon loading with fallback
    try:
        app_icon = QIcon(os.path.join(os.path.dirname(__file__), "icon.png"))
    except:
        app_icon = None

    def __init__(self, user_account):
        super().__init__(user_account)
        self.last_job_time = 0
        self.cooldown_seconds = 30
        self.current_task_type = None
        self.cooldown_timer = None

        self._init_ui()
        self._new_task()

    def _init_ui(self):
        self.setWindowTitle("gJobs - Task Center")
        layout = QVBoxLayout()
        self.setLayout(layout)

        self.info_label = QLabel("Complete the task below to earn gBalance.")
        self.info_label.setFont(QFont("Segoe UI", 14))
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.info_label)

        self.task_label = QLabel()
        self.task_label.setFont(QFont("Courier New", 18))
        self.task_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.task_label)

        self.input_line = QLineEdit()
        self.input_line.setFont(QFont("Segoe UI", 14))
        self.input_line.setPlaceholderText("Enter answer or text here")
        layout.addWidget(self.input_line)

        self.click_button = QPushButton("Click Me!")
        self.click_button.setVisible(False)
        self.click_button.clicked.connect(self._handle_click_task)
        layout.addWidget(self.click_button)

        self.submit_button = QPushButton("Submit Answer")
        self.submit_button.clicked.connect(self._submit_task)
        layout.addWidget(self.submit_button)

        self.cooldown_label = QLabel("")
        self.cooldown_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.cooldown_label)

        self._update_cooldown_label()

    def _new_task(self):
        self.input_line.clear()
        self.click_button.setVisible(False)

        self.current_task_type = random.choice(["math", "typing", "click"])

        if self.current_task_type == "math":
            self.a = random.randint(10, 99)
            self.b = random.randint(10, 99)
            self.task_label.setText(f"What is {self.a} + {self.b}?")

        elif self.current_task_type == "typing":
            long_words = [
                "calibration", "disintegration", "optimization",
                "quantifiable", "infrastructure", "manipulation",
                "synchronization", "discombobulation"
            ]

            self.target_word = random.choice(long_words)
            self.task_label.setText(f"Type this exactly: {self.target_word}")

        elif self.current_task_type == "click":
            self.required_clicks = random.randint(50, 100)
            self.click_count = 0
            self.task_label.setText(f"Click the button {self.required_clicks} times!")
            self.click_button.setVisible(True)

    def _handle_click_task(self):
        if self._is_on_cooldown():
            self._show_cooldown_message()
            return

        self.click_count += 1
        remaining = self.required_clicks - self.click_count
        if remaining > 0:
            self.task_label.setText(f"{remaining} clicks to go...")
        else:
            self._complete_task()

    def _submit_task(self):
        if self._is_on_cooldown():
            self._show_cooldown_message()
            return

        if self.current_task_type == "math":
            try:
                user_answer = int(self.input_line.text())
                if user_answer == self.a + self.b:
                    self._complete_task()
                else:
                    QMessageBox.warning(self, "Wrong", "That's not correct.")
            except ValueError:
                QMessageBox.warning(self, "Invalid", "Please enter a number.")

        elif self.current_task_type == "typing":
            if self.input_line.text().strip() == self.target_word:
                self._complete_task()
            else:
                QMessageBox.warning(self, "Wrong", "Incorrect typing.")

    def _complete_task(self):
        self.last_job_time = time.time()

        # Define rewards for each task type
        rewards = {
            "math": 5.0,
            "typing": 4.0,
            "click": 2.0
        }
        reward = rewards.get(self.current_task_type, 1.0)

        self.user.increment_field("gBalance", reward)
        self.user.log_transaction(
            type_="income",
            amount=reward,
            description=f"Earned from gJobs ({self.current_task_type} task)"
        )

        QMessageBox.information(self, "Success", f"You earned {reward:.1f} gBalance!")
        self._update_cooldown_label()
        self._start_cooldown_timer()
        self._new_task()

    def _is_on_cooldown(self):
        return time.time() - self.last_job_time < self.cooldown_seconds

    def _show_cooldown_message(self):
        QMessageBox.information(self, "Cooldown", "You must wait before doing another job.")

    def _update_cooldown_label(self):
        remaining = max(0, self.cooldown_seconds - int(time.time() - self.last_job_time))
        if remaining > 0:
            self.cooldown_label.setText(f"Next job in: {remaining} seconds")
        else:
            self.cooldown_label.setText("")

    def _start_cooldown_timer(self):
        # Stop existing timer if running to prevent multiple timers
        if self.cooldown_timer:
            self.cooldown_timer.stop()

        self.cooldown_timer = QTimer(self)
        self.cooldown_timer.timeout.connect(self._update_cooldown_label)
        self.cooldown_timer.start(1000)
