import sys
from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QStackedWidget
from sdk.style import GNET_STYLESHEET
from core.login import LoginScreen
from core.dashboard import Dashboard
from user.manager import UserManager

class gNetLauncher(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("gNet")
        self.setGeometry(100, 100, 625, 400)

        self.stack = QStackedWidget()
        self.setCentralWidget(self.stack)

        self.user_manager = UserManager()  # manage user accounts
        self.login_screen = LoginScreen(self.user_manager)
        self.login_screen.login_successful.connect(self.on_login_success)
        self.stack.addWidget(self.login_screen)

        self.main_screen = None  # created after login

    def on_login_success(self, user_account):
        self.main_screen = Dashboard(user_account)
        self.stack.addWidget(self.main_screen)
        self.stack.setCurrentWidget(self.main_screen)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyleSheet(GNET_STYLESHEET)
    launcher = gNetLauncher()
    launcher.show()
    sys.exit(app.exec())