# apps/gClicker/main.py

import os
from PyQt6.QtGui import QIcon, QFont
from PyQt6.QtWidgets import Q<PERSON>ox<PERSON><PERSON>out, QLabel
from PyQt6.QtCore import Qt

from sdk.app_base import GNetAppBase

from apps.gClicker.app.ui.main_view import MainView
from apps.gClicker.app.core.materials.manager import MaterialManager
from apps.gClicker.app.core.power.manager import PowerManager
from apps.gClicker.app.core.economy.upgrade_store import UpgradeStore
from apps.gClicker.app.core.crafting.manager import CraftingManager
from apps.gClicker.app.state.save_manager import SaveManager


class gClicker(GNetAppBase):
    app_name = "gClicker"
    app_version = "1.0.0"
    app_description = "A complex incremental resource clicker built inside gNet."
    app_icon = QIcon(os.path.join(os.path.dirname(__file__), "clicker_icon.png"))

    def __init__(self, user_account):
        super().__init__(user_account)

        # Set up save system
        self.save_manager = SaveManager(self.user.data)
        gclicker_data = self.save_manager.get_gclicker_data()

        # Managers
        self.material_manager = MaterialManager(gclicker_data["materials"])
        self.power_manager = PowerManager(gclicker_data["power"])
        self.upgrade_store = UpgradeStore(gclicker_data["upgrades"])
        self.crafting_manager = CraftingManager(self.material_manager)

        # UI
        self.view = MainView(
            material_manager=self.material_manager,
            power_manager=self.power_manager,
            upgrade_store=self.upgrade_store,
            crafting_manager=self.crafting_manager,
            save_callback=self.save_manager.save
        )

        self.setLayout(QVBoxLayout())
        self.layout().addWidget(self.view)

    def closeEvent(self, event):
        self.save_manager.save()
        super().closeEvent(event)
