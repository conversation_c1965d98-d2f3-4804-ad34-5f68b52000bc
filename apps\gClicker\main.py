# apps/gClicker/main.py - LITE VERSION

import os
from PyQt6.QtGui import QIcon, QFont
from PyQt6.QtWidgets import <PERSON><PERSON>ox<PERSON>ayout, QLabel, QPushButton, QTabWidget, QWidget
from PyQt6.QtCore import Qt, QTimer

from sdk.app_base import GNetAppBase
from .material_manager import MaterialManager
from .materials_tab import MaterialsTab

class gClicker(GNetAppBase):
    app_name = "gClicker"
    app_version = "1.0.0"
    app_description = "A simple incremental clicker game."
    app_icon = QIcon(os.path.join(os.path.dirname(__file__), "clicker_icon.png"))

    def __init__(self, user_account):
        super().__init__(user_account)
        
        # Initialize data for clicking mechanics
        self.data = self.user.get_field("gclicker", {
            "currency": 0,
            "click_power": 1,
            "passive_income": 0,
            "upgrades_owned": [],
        })
        # Initialize material manager
        self.material_manager = MaterialManager(self.data)
        
        self._init_ui()
        self._start_passive_timer()

    def _init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Title
        title = QLabel("gClicker Lite")
        title.setFont(QFont("Segoe UI", 24))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Create tab widget
        self.tabs = QTabWidget()
        layout.addWidget(self.tabs)

        # Clicking tab
        self.clicking_tab = self._create_clicking_tab()
        self.tabs.addTab(self.clicking_tab, "Clicking")

        # Materials tab
        self.materials_tab = MaterialsTab(self.material_manager, self._on_materials_update)
        self.tabs.addTab(self.materials_tab, "Materials")

    def _create_clicking_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # Currency display
        self.currency_label = QLabel(f"Currency: {self.data['currency']}")
        self.currency_label.setFont(QFont("Segoe UI", 16))
        self.currency_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.currency_label)

        # Click power display
        self.power_label = QLabel(f"Click Power: {self.data['click_power']}")
        self.power_label.setFont(QFont("Segoe UI", 14))
        self.power_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.power_label)

        # Passive income display
        self.passive_label = QLabel(f"Passive Income: {self.data['passive_income']}/sec")
        self.passive_label.setFont(QFont("Segoe UI", 14))
        self.passive_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.passive_label)

        # Click button
        self.click_button = QPushButton("Click Me!")
        self.click_button.setFont(QFont("Segoe UI", 18))
        self.click_button.clicked.connect(self.handle_click)
        layout.addWidget(self.click_button, alignment=Qt.AlignmentFlag.AlignCenter)

        # Upgrades section
        upgrades_label = QLabel("Upgrades:")
        upgrades_label.setFont(QFont("Segoe UI", 16))
        layout.addWidget(upgrades_label)

        # Upgrade buttons
        self.upgrade_buttons = {}
        self.normal_upgrades = [
            ("Iron Finger", 50, "power", 1),
            ("Steel Knuckle", 200, "power", 5),
            ("Auto-Clicker I", 250, "passive", 10),
            ("Auto-Clicker II", 500, "passive", 15),
            ("Auto-Clicker III", 1500, "passive", 25),
        ]
        # Add both normal and copper click upgrades to the UI
        for name, cost, effect_type, value in self.normal_upgrades:
            btn = QPushButton(f"{name} ({cost})")
            btn.clicked.connect(lambda checked, n=name, c=cost, e=effect_type, v=value: self.buy_normal_upgrade(n, c, e, v))
            self.upgrade_buttons[name] = btn
            layout.addWidget(btn)
        self.copper_click_upgrades = [
            {"name": "Copper Clicker I", "cost_currency": 2500, "cost_material": ("copper", "raw", 100), "effect": ("click_power", 10)},
            {"name": "Copper Clicker II", "cost_currency": 5000, "cost_material": ("copper", "refined", 200), "effect": ("click_power", 25)},
            {"name": "Copper Clicker III", "cost_currency": 12500, "cost_material": ("copper", "pure", 200), "effect": ("passive_income", 400)},
        ]
        for upgrade in self.copper_click_upgrades:
            btn = QPushButton(f"{upgrade['name']} ({upgrade['cost_currency']} currency, {upgrade['cost_material'][2]} {upgrade['cost_material'][0]})")
            btn.clicked.connect(lambda checked, u=upgrade: self.buy_upgrade(u['name'], u['cost_currency'], u['cost_material'], u['effect']))
            self.upgrade_buttons[upgrade['name']] = btn
            layout.addWidget(btn)

        self._update_upgrade_buttons()
        return tab

    def handle_click(self):
        self.data["currency"] += self.data["click_power"]
        self._update_display()
        self._update_upgrade_buttons()
        self.materials_tab.refresh()  # Refresh MaterialsTab
        self._save_data()

    def buy_upgrade(self, name, cost_currency, cost_material, effect):
        if self.data["currency"] >= cost_currency and self.material_manager.has_material(cost_material[0], cost_material[1], cost_material[2]):
            self.data["currency"] -= cost_currency
            self.material_manager.use_material(cost_material[0], cost_material[1], cost_material[2])
            
            if effect[0] == "click_power":
                self.data["click_power"] += effect[1]
            elif effect[0] == "passive_income":
                self.data["passive_income"] += effect[1]
            
            self.data["upgrades_owned"].append(name)
            
            self._update_display()
            self._update_upgrade_buttons()
            self.materials_tab.refresh()  # Refresh MaterialsTab
            self._save_data()

    def buy_normal_upgrade(self, name, cost, effect_type, value):
        if self.data["currency"] >= cost and name not in self.data["upgrades_owned"]:
            self.data["currency"] -= cost
            self.data["upgrades_owned"].append(name)
            if effect_type == "power":
                self.data["click_power"] += value
            elif effect_type == "passive":
                self.data["passive_income"] += value
            self._update_display()
            self._update_upgrade_buttons()
            self.materials_tab.refresh()
            self._save_data()

    def _update_display(self):
        self.currency_label.setText(f"Currency: {self.data['currency']}")
        self.power_label.setText(f"Click Power: {self.data['click_power']}")
        self.passive_label.setText(f"Passive Income: {self.data['passive_income']}/sec")

    def _update_upgrade_buttons(self):
        for name, btn in self.upgrade_buttons.items():
            if name in self.data["upgrades_owned"]:
                btn.setText(f"{name} (OWNED)")
                btn.setEnabled(False)
            else:
                # Check if this is a copper click upgrade
                copper_upgrade = next((u for u in self.copper_click_upgrades if u['name'] == name), None)
                if copper_upgrade:
                    cost_currency = copper_upgrade['cost_currency']
                    cost_material = copper_upgrade['cost_material']
                    # Show copper type in button text
                    btn.setText(f"{name} ({cost_currency} currency, {cost_material[2]} {cost_material[1]} copper)")
                    btn.setEnabled(self.data["currency"] >= cost_currency and self.material_manager.has_material(cost_material[0], cost_material[1], cost_material[2]))
                else:
                    # Normal upgrade (currency only)
                    normal_upgrade = next((u for u in self.normal_upgrades if u[0] == name), None)
                    if normal_upgrade:
                        cost = normal_upgrade[1]
                        btn.setText(f"{name} ({cost})")
                        btn.setEnabled(self.data["currency"] >= cost)

    def _on_materials_update(self):
        self._update_display()
        self._update_upgrade_buttons()
        self.materials_tab.refresh()  # Refresh MaterialsTab
        self._save_data()

    def _start_passive_timer(self):
        # Stop any existing timer first
        if hasattr(self, 'passive_timer'):
            self.passive_timer.stop()
        self.passive_timer = QTimer()
        self.passive_timer.timeout.connect(self._game_tick)
        self.passive_timer.start(1000)  # Every second

    def _game_tick(self):
        # Passive income for currency
        if self.data["passive_income"] > 0:
            self.data["currency"] += self.data["passive_income"]
        # Auto-produce materials
        self.material_manager.auto_produce_all()
        self._update_display()
        self._update_upgrade_buttons()
        self.materials_tab.refresh()
        self._save_data()

    def _save_data(self):
        # Save both main data and material data
        self.user.set_field("gclicker", self.data)
        self.material_manager.save_all()

    def stop(self):
        """Called when the tab is closed - clean up timers"""
        if hasattr(self, 'passive_timer'):
            self.passive_timer.stop()
        self._save_data()

    def closeEvent(self, event):
        # Stop the timer when closing
        if hasattr(self, 'passive_timer'):
            self.passive_timer.stop()
        self._save_data()
        super().closeEvent(event)
