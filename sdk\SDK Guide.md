# gNet SDK Quickstart Guide for App Developers

Welcome to the gNet SDK! This guide will help you build your first app in the gNet ecosystem by walking you through user data access, transactions, and basic app structure.

---

## 1. SDK Overview

* **gNet** is a Python-based platform for building user-account-driven apps.
* Each user has a secure account with hashed passwords and persistent data.
* Apps extend `GNetAppBase` and get access to the logged-in user's data through a `User` object.
* User data is stored in JSON and can include balances, inventory, progress, or custom fields.
* You can safely read, write, increment, and decrement data, plus log transactions for tracking.

---

## 2. The `User` Class

`User` represents the logged-in user and provides methods for:

* **Authentication**

  * `check_password(password: str) -> bool`: Verify passwords.
* **Data Access**

  * `get_field(key: str, default=None)`: Read data safely.
  * `set_field(key: str, value)`: Write data and auto-save.
* **Data Manipulation**

  * `increment_field(key: str, amount=1)`: Increase numeric fields.
  * `decrement_field(key: str, amount=1, allow_negative=False)`: Decrease numeric fields with optional negative value protection.
* **Transactions**

  * `log_transaction(type_: str, amount: float, description=None)`: Record balance or event changes.

---

## 3. Common Usage Patterns

### Reading and Updating gBalance

```python
current_balance = user.get_field("gBalance", 0.0)
user.set_field("gBalance", current_balance + 50)
```

### Safely Adding Currency

```python
user.increment_field("gBalance", 10)
user.log_transaction("reward", 10, "Bonus from completing task")
```

### Spending Currency with Checks

```python
cost = 25
if user.get_field("gBalance", 0) >= cost:
    user.decrement_field("gBalance", cost)
    user.log_transaction("purchase", cost, "Bought item X")
else:
    print("Not enough balance!")
```

---

## 4. Creating Your App

Your app’s main class extends `GNetAppBase` and receives a `User` instance on init:

```python
from sdk.app_base import GNetAppBase

class MyApp(GNetAppBase):
    app_name = "My First gNet App"
    app_version = "0.1.0"
    app_description = "An example app."

    def __init__(self, user):
        super().__init__(user)
        # Access user data:
        balance = user.get_field("gBalance", 0)
        # Initialize your UI and logic here
```

---

## 5. Tips and Best Practices

* **Always check and validate user data before spending or incrementing.**
* **Log transactions whenever the user’s balance changes.** This helps with debugging and auditing.
* **Balance your app’s gBalance economy carefully** to avoid inflation or exploits.
* Use `set_field` for non-numeric data (e.g., strings, lists).
* Use the default app icon or provide your own for branding.
* Increment and decrement methods automatically save user data; no need to call save manually afterward.

---

## 6. Sample Minimal App Code

```python
from sdk.app_base import GNetAppBase
from PyQt6.QtWidgets import QLabel, QVBoxLayout, QPushButton

class SampleApp(GNetAppBase):
    app_name = "Sample Rewarder"
    app_version = "0.1.0"
    app_description = "Gives you 5 gBalance when you click the button."

    def __init__(self, user):
        super().__init__(user)
        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        self.balance_label = QLabel(f"Balance: {user.get_field('gBalance', 0)}")
        self.layout.addWidget(self.balance_label)

        self.reward_button = QPushButton("Get 5 gBalance")
        self.reward_button.clicked.connect(self.give_reward)
        self.layout.addWidget(self.reward_button)

    def give_reward(self):
        self.user.increment_field("gBalance", 5)
        self.user.log_transaction("reward", 5, "Clicked reward button")
        self.balance_label.setText(f"Balance: {self.user.get_field('gBalance', 0)}")
```

---

If you ever need help on the fly with a specific user method, know every user method has a docstring.