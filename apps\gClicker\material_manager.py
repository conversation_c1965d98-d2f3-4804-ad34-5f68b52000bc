from typing import Dict, List, Optional
from apps.gClicker.material_base import Material, MaterialTier
from apps.gClicker.materials.basic.copper import Copper
from apps.gClicker.materials.basic.tin import Tin
from apps.gClicker.materials.basic.iron import Iron
from apps.gClicker.materials.basic.zinc import Zinc
from apps.gClicker.materials.alloys.bronze import Bronze

class MaterialManager:
    def __init__(self, root_data: dict):
        self.root_data = root_data
        self.user_data = root_data.setdefault("materials", {})
        self.materials: Dict[str, Material] = {}
        self._load_materials()
        self._load_material_data()
    
    def _load_materials(self):
        """Initialize all available materials"""
        self.materials["copper"] = Copper()
        self.materials["tin"] = Tin()
        self.materials["iron"] = Iron()
        self.materials["zinc"] = Zinc()
        self.materials["bronze"] = Bronze()
        # Add more materials here soon
    
    def _load_material_data(self):
        """Load saved material data"""
        for material_name, material in self.materials.items():
            if material_name in self.user_data:
                data = self.user_data[material_name]
                # Handle both old format (int) and new format (dict)
                if isinstance(data, int):
                    # Old format: just a number, convert to new format
                    material.storage["raw"] = data
                elif isinstance(data, dict):
                    # New format: dictionary with full material data
                    material.from_dict(data)
    
    def get_material(self, name: str) -> Optional[Material]:
        """Get a material by name"""
        return self.materials.get(name)
    
    def get_all_materials(self) -> List[Material]:
        """Get all materials"""
        return list(self.materials.values())
    
    def get_materials_by_tier(self, tier: MaterialTier) -> List[Material]:
        """Get materials of a specific tier"""
        return [material for material in self.materials.values() if material.tier == tier]
    
    def produce_material(self, material_name: str, amount: float = None) -> float:
        """Produce a specific material"""
        material = self.get_material(material_name)
        if not material:
            return 0
        
        produced = material.produce(amount)
        self._save_material_data(material_name)
        return produced
    
    def refine_material(self, material_name: str, amount: float = None) -> float:
        """Refine a specific material"""
        material = self.get_material(material_name)
        if not material:
            return 0
        
        refined = material.refine(amount)
        self._save_material_data(material_name)
        return refined
    
    def buy_material_upgrade(self, material_name: str, upgrade_name: str) -> bool:
        """Buy an upgrade for a specific material"""
        material = self.get_material(material_name)
        if not material:
            return False
        # Find the upgrade
        upgrade = None
        for available_upgrade in material.get_available_upgrades():
            if available_upgrade.name == upgrade_name:
                upgrade = available_upgrade
                break
        if not upgrade:
            return False
        # Try to buy the upgrade
        currency = self.get_currency()
        if material.can_buy_upgrade(upgrade, currency, self):
            # Deduct currency
            if isinstance(upgrade.cost, int):
                self.root_data["currency"] = currency - upgrade.cost
            elif isinstance(upgrade.cost, tuple):
                self.root_data["currency"] = currency - upgrade.cost[0]
            material.buy_upgrade(upgrade, currency, self)
            self._save_material_data(material_name)
            self._save_currency()
            return True
        return False
    
    def get_currency(self) -> int:
        return self.root_data.get("currency", 0)
    
    def add_currency(self, amount: int):
        self.root_data["currency"] = self.get_currency() + amount
    
    def spend_currency(self, amount: int) -> bool:
        if self.get_currency() >= amount:
            self.root_data["currency"] = self.get_currency() - amount
            return True
        return False
    
    def _save_material_data(self, material_name: str):
        """Save a specific material's data"""
        material = self.get_material(material_name)
        if material:
            self.user_data[material_name] = material.to_dict()
    
    def _save_currency(self):
        pass  # No longer needed, currency is always in root_data
    
    def save_all(self):
        """Save all material data"""
        for material_name in self.materials.keys():
            self._save_material_data(material_name)
        self._save_currency()

    def auto_produce_all(self):
        for material in self.materials.values():
            if material.unlocked:
                material.produce()
                if material.can_refine and material.auto_refine_enabled:
                    material.refine()
                if material.can_purify and material.auto_purify_enabled:
                    material.purify()
                # Handle alloying for alloy materials
                if hasattr(material, 'can_alloy') and material.can_alloy and material.auto_alloy_enabled:
                    material.alloy(self)
        self.save_all()

    def unlock_material(self, material_name: str) -> bool:
        material = self.get_material(material_name)
        if not material or material.unlocked:
            return False
        if self.get_currency() >= material.unlock_cost:
            self.root_data["currency"] -= material.unlock_cost
            material.unlocked = True
            self._save_material_data(material_name)
            return True
        return False

    def has_material(self, material_name: str, state: str, amount: int) -> bool:
        material = self.get_material(material_name)
        if not material:
            return False
        return material.storage.get(state, 0) >= amount

    def use_material(self, material_name: str, state: str, amount: int) -> bool:
        material = self.get_material(material_name)
        if not material:
            return False
        if material.storage.get(state, 0) >= amount:
            material.storage[state] -= amount
            self._save_material_data(material_name)
            return True
        return False
    
    def get_material_amount(self, material_name: str, state: str) -> int:
        """Get the amount of a specific material in a specific state"""
        material = self.get_material(material_name)
        if not material:
            return 0
        return material.storage.get(state, 0) 