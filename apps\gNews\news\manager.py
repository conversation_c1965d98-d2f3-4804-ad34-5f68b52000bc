from .models import Article, Comment
from .storage import NewsStorage
from typing import List
from datetime import datetime
import uuid

class NewsManager:
    def __init__(self):
        self.storage = NewsStorage()
        self.articles = self.storage.load_articles()

    def add_article(self, title: str, summary: str, content: str, author: str):
        article = Article(
            id=str(uuid.uuid4()),
            title=title,
            summary=summary,
            content=content,
            author=author,
            timestamp=datetime.now(),
            comments=[]
        )
        self.articles.insert(0, article)  # newest first
        self.storage.save_articles(self.articles)

    def get_articles(self) -> List[Article]:
        return self.articles

    def add_comment(self, article_id: str, author: str, content: str):
        for article in self.articles:
            if article.id == article_id:
                comment = Comment(author=author, content=content, timestamp=datetime.now())
                article.comments.append(comment)
                self.storage.save_articles(self.articles)
                break 