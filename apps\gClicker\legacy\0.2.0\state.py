# state.py

class ClickerState:
    def __init__(self, user_account):
        self.user_account = user_account
        self.data = user_account.get_field("clicker", {
            "clicks": 0,
            "click_value": 1,
            "upgrades": [],
            "crit_chance": 0,
            "crit_multiplier": 2,
            "passive_income": 0,
        })
        self.save()

    def save(self):
        self.user_account.set_field("clicker", self.data)