from enum import Enum
from apps.gClicker.app.core.materials.tiers import MaterialTier


class MaterialProcessStage(Enum):
    RAW = "raw"
    REFINED = "refined"
    PURE = "pure"


class BaseMaterial:
    """Base class for all materials."""

    name: str = None
    tier: MaterialTier = None

    # Default refinement and purification ratios
    raw_to_refined_ratio: int = 5
    refined_to_pure_ratio: int = 4

    def __init__(self, user_data: dict):
        if self.name is None or self.tier is None:
            raise ValueError(f"{self.__class__.__name__} must define `name` and `tier`")

        self.user_data = user_data

        if self.name not in self.user_data:
            self.user_data[self.name] = {
                "raw": 0,
                "refined": 0,
                "pure": 0
            }

    def get_storage(self) -> dict:
        return self.user_data[self.name]

    def add_raw(self, amount: int):
        self.get_storage()["raw"] += amount

    def refine(self, amount: int) -> int:
        available = self.get_storage()["raw"]
        amount = min(amount, available)
        output = amount // self.raw_to_refined_ratio
        self.get_storage()["raw"] -= output * self.raw_to_refined_ratio
        self.get_storage()["refined"] += output
        return output

    def purify(self, amount: int) -> int:
        available = self.get_storage()["refined"]
        amount = min(amount, available)
        output = amount // self.refined_to_pure_ratio
        self.get_storage()["refined"] -= output * self.refined_to_pure_ratio
        self.get_storage()["pure"] += output
        return output

    def get_totals(self) -> dict:
        return self.get_storage().copy()

    def reset(self):
        self.user_data[self.name] = {
            "raw": 0,
            "refined": 0,
            "pure": 0
        }