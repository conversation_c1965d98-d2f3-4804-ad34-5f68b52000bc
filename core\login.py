from PyQt6.QtWidgets import (
    QWidget, QLabel, QVBoxLayout, QHBoxLayout, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from sdk.widgets import GNetButton, GNetInput


class LoginScreen(QWidget):
    login_successful = pyqtSignal(object)  # Signal to emit when login is successful

    def __init__(self, user_manager):
        super().__init__()
        self.user_manager = user_manager

        outer_layout = QHBoxLayout()
        outer_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setLayout(outer_layout)

        frame = QFrame()
        frame.setFixedSize(320, 320)
        inner_layout = QVBoxLayout()
        inner_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame.setLayout(inner_layout)

        self.title = QLabel("Welcome to gNet")
        self.title.setFont(QFont("Segoe UI", 28))
        self.title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.username_input = GNetInput()
        self.username_input.setPlaceholderText("Enter username...")

        self.password_input = GNetInput()
        self.password_input.setPlaceholderText("Enter password...")
        self.password_input.setEchoMode(GNetInput.EchoMode.Password)

        self.error_label = QLabel("")
        self.error_label.setStyleSheet("color: #FF5555; font-weight: bold;")
        self.error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.error_label.setVisible(False)

        self.login_button = GNetButton("Login")
        self.login_button.clicked.connect(self.handle_login)

        self.register_button = GNetButton("Register")
        self.register_button.clicked.connect(self.handle_register)

        inner_layout.addWidget(self.title)
        inner_layout.addSpacing(10)
        inner_layout.addWidget(self.username_input)
        inner_layout.addSpacing(10)
        inner_layout.addWidget(self.password_input)
        inner_layout.addSpacing(10)
        inner_layout.addWidget(self.error_label)
        inner_layout.addSpacing(10)
        inner_layout.addWidget(self.login_button)
        inner_layout.addSpacing(5)
        inner_layout.addWidget(self.register_button)

        outer_layout.addWidget(frame)

    def handle_login(self):
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not username or not password:
            self.set_error("Enter both username and password.")
            return

        try:
            user = self.user_manager.login(username, password)
            self.set_error("")
            self.login_successful.emit(user)  # Emit signal with user object
        except ValueError as e:
            self.set_error(str(e))

    def handle_register(self):
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not username or not password:
            self.set_error("Enter both username and password.")
            return

        if self.user_manager.get_user(username):
            self.set_error("Username already exists.")
            return

        user = self.user_manager.create_user(username, password)
        self.user_manager.login(username, password)
        self.set_error("")
        self.login_successful.emit(user)  # Emit signal with user object

    def set_error(self, message):
        self.error_label.setText(message)
        self.error_label.setVisible(bool(message))