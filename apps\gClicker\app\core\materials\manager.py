from apps.gClicker.app.core.materials.basic import Copper, Iron, Tin, Wood, Stone, Coal
from apps.gClicker.app.core.materials.moderate import Zinc, Nickel, Aluminum, Silicon, Oil, Limestone
from apps.gClicker.app.core.materials.advanced import Titanium, Tungsten, Uranium, Neodymium, Graphene, Platinum
from apps.gClicker.app.core.materials.insane import Antimatter, DarkMatter, RedMercury, VoidCrystal

from apps.gClicker.app.core.materials.alloys import (
    Bronze, Brass, Duralumin, Invar, QuantumSteel as AlloyQuantumSteel,
    StainlessSteel, Steel, TitaniumAlloy
)

from apps.gClicker.app.core.materials.base import BaseMaterial
from apps.gClicker.app.core.materials.alloys.base import AlloyBase
from apps.gClicker.app.core.materials.tiers import MaterialTier


class MaterialManager:
    def __init__(self, user_data: dict):
        self.user_data = user_data.setdefault("materials", {})

        # Instantiate all materials
        self.materials: dict[str, BaseMaterial] = {}
        self.alloys: dict[str, AlloyBase] = {}

        self._load_materials()
        self._load_alloys()

    def _load_materials(self):
        for cls in [
            Copper, Iron, Tin, Wood, Stone, Coal,
            Zinc, Nickel, Aluminum, Silicon, Oil, Limestone,
            Titanium, Tungsten, Uranium, Neodymium, Graphene, Platinum,
            Antimatter, DarkMatter, RedMercury, VoidCrystal
        ]:
            inst = cls(self.user_data)
            self.materials[inst.name] = inst

    def _load_alloys(self):
        for cls in [
            Bronze, Brass, Duralumin, Invar, AlloyQuantumSteel,
            StainlessSteel, Steel, TitaniumAlloy
        ]:
            inst = cls(self.user_data)
            self.alloys[inst.name] = inst

    def get_material(self, name: str) -> BaseMaterial | None:
        return self.materials.get(name)

    def get_alloy(self, name: str) -> AlloyBase | None:
        return self.alloys.get(name)

    def get_all_materials(self) -> list[BaseMaterial]:
        return list(self.materials.values())

    def get_all_alloys(self) -> list[AlloyBase]:
        return list(self.alloys.values())

    def get_materials_by_tier(self, tier: MaterialTier) -> list[BaseMaterial]:
        """Get all materials of a specific tier."""
        return [material for material in self.materials.values() if material.tier == tier]

    def refine_all(self):
        for material in self.materials.values():
            material.refine(material.get_storage()["raw"])

    def purify_all(self):
        for material in self.materials.values():
            material.purify(material.get_storage()["refined"])

    def reset_all(self):
        for material in self.materials.values():
            material.reset()
        # Alloys don’t need resets; they are crafted, not refined