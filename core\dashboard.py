from PyQt6.QtWidgets import (
    QWidget, QVBox<PERSON>ayout, QLabel, QPushButton, QScrollArea,
    QSizePolicy, QTabWidget, QTabBar, QGridLayout, QToolButton
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QIcon
from core.app_discovery import discover_apps


class AppIconButton(QToolButton):
    def __init__(self, app_info, launch_callback):
        super().__init__()
        self.app_info = app_info
        self.launch_callback = launch_callback

        self.setIcon(app_info["icon"] if app_info["icon"] else QIcon("assets/default_icon.png"))
        self.setIconSize(QSize(64, 64))
        self.setFixedSize(100, 100)

        self.setText(app_info["name"])
        self.setToolTip(app_info["description"])

        self.setStyleSheet("""
            QToolButton {
                border: none;
                background-color: transparent;
                padding-top: 5px;
                font-size: 14px;
            }
            QToolButton:hover {
                background-color: rgba(50, 50, 50, 0.5);
                border-radius: 10px;
            }
        """)

        # This method exists on QToolButton
        self.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)

        self.clicked.connect(lambda: self.launch_callback(self.app_info))


class Dashboard(QWidget):
    def __init__(self, user_account):
        super().__init__()
        self.user_account = user_account
        self.apps = discover_apps()

        self.setWindowTitle("gNet Dashboard")
        self.resize(800, 600)

        self.main_layout = QVBoxLayout()
        self.setLayout(self.main_layout)

        self.tabs = QTabWidget()
        self.tabs.setTabsClosable(True)
        self.tabs.tabCloseRequested.connect(self.close_tab)
        self.main_layout.addWidget(self.tabs)

        # Apps list tab (non-closable)
        self.apps_tab = QWidget()
        self.tabs.addTab(self.apps_tab, "Apps")
        self.tabs.tabBar().setTabButton(0, QTabBar.ButtonPosition.RightSide, None)

        self.apps_layout = QVBoxLayout()
        self.apps_tab.setLayout(self.apps_layout)

        self.apps_area = QScrollArea()
        self.apps_area.setWidgetResizable(True)
        self.apps_layout.addWidget(self.apps_area)

        self.apps_container = QWidget()
        self.apps_container_layout = QGridLayout()
        self.apps_container.setLayout(self.apps_container_layout)
        self.apps_container_layout.setSpacing(15)
        self.apps_container_layout.setContentsMargins(10, 10, 10, 10)
        self.apps_area.setWidget(self.apps_container)

        self.load_app_buttons()

    def load_app_buttons(self):
        cols = 5
        for i, app in enumerate(self.apps):
            btn = AppIconButton(app, self.launch_app_tab)
            row, col = divmod(i, cols)
            self.apps_container_layout.addWidget(btn, row, col, alignment=Qt.AlignmentFlag.AlignCenter)

    def launch_app_tab(self, app_info):
        # Avoid opening duplicates
        for i in range(1, self.tabs.count()):
            widget = self.tabs.widget(i)
            if getattr(widget, "app_name", None) == app_info["name"]:
                self.tabs.setCurrentIndex(i)
                return

        app_class = app_info["class"]
        app_instance = app_class(self.user_account)

        # Set app metadata for future tab reference
        app_instance.app_name = app_info["name"]

        self.tabs.addTab(app_instance, app_info["name"])
        self.tabs.setCurrentWidget(app_instance)

    def close_tab(self, index):
        if index == 0:
            return  # Don't close the Apps tab
        widget = self.tabs.widget(index)
        if widget and hasattr(widget, "stop"):
            widget.stop()
        self.tabs.removeTab(index)
