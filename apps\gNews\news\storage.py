import os
import json
from typing import List
from .models import Article, Comment
from datetime import datetime

NEWS_DATA_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../data'))
NEWS_FILE = os.path.join(NEWS_DATA_DIR, 'articles.json')

class NewsStorage:
    def __init__(self):
        os.makedirs(NEWS_DATA_DIR, exist_ok=True)

    def save_articles(self, articles: List[Article]):
        with open(NEWS_FILE, 'w', encoding='utf-8') as f:
            json.dump([
                {
                    'id': a.id,
                    'title': a.title,
                    'summary': a.summary,
                    'content': a.content,
                    'author': a.author,
                    'timestamp': a.timestamp.isoformat(),
                    'comments': [
                        {'author': c.author, 'content': c.content, 'timestamp': c.timestamp.isoformat()}
                        for c in a.comments
                    ]
                }
                for a in articles
            ], f, indent=2)

    def load_articles(self) -> List[Article]:
        if not os.path.exists(NEWS_FILE):
            return []
        with open(NEWS_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            articles = []
            for a in data:
                comments = [
                    Comment(author=c['author'], content=c['content'], timestamp=datetime.fromisoformat(c['timestamp']))
                    for c in a.get('comments', [])
                ]
                articles.append(Article(
                    id=a['id'],
                    title=a['title'],
                    summary=a['summary'],
                    content=a['content'],
                    author=a['author'],
                    timestamp=datetime.fromisoformat(a['timestamp']),
                    comments=comments
                ))
            return articles 