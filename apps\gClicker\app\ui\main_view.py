from PyQt6.QtWidgets import Q<PERSON><PERSON>Window, QTabWidget, QWidget, QVBoxLayout
from apps.gClicker.app.ui.materials_tab import MaterialsTab
from apps.gClicker.app.ui.power_tab import PowerTab
from apps.gClicker.app.ui.crafting_tab import CraftingTab
from apps.gClicker.app.ui.upgrades_tab import UpgradesTab

from apps.gClicker.app.core.materials.manager import MaterialManager
from apps.gClicker.app.core.power.manager import PowerManager
from apps.gClicker.app.core.crafting.manager import CraftingManager
from apps.gClicker.app.core.economy.upgrade_store import UpgradeStore

from apps.gClicker.app.state.save_manager import save_user_data


class MainView(QMainWindow):
    def __init__(self, material_manager: MaterialManager, power_manager: PowerManager,
                 upgrade_store: UpgradeStore, crafting_manager: CraftingManager,
                 save_callback=None):
        super().__init__()
        self.setWindowTitle("gClicker")

        # Store managers
        self.material_manager = material_manager
        self.power_manager = power_manager
        self.crafting_manager = crafting_manager
        self.upgrade_store = upgrade_store
        self.save_callback = save_callback

        self._init_ui()

    def _init_ui(self):
        tabs = QTabWidget()

        # Tabs - need to check what parameters these constructors expect
        self.materials_tab = MaterialsTab(self.material_manager.user_data)
        self.power_tab = PowerTab(self.power_manager.user_data)
        self.crafting_tab = CraftingTab(self.material_manager.user_data, self.power_manager.user_data)
        self.upgrades_tab = UpgradesTab(self.upgrade_store, self._on_upgrade_bought)

        tabs.addTab(self.materials_tab, "Materials")
        tabs.addTab(self.power_tab, "Power")
        tabs.addTab(self.crafting_tab, "Crafting")
        tabs.addTab(self.upgrades_tab, "Upgrades")

        # Layout
        central_widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(tabs)
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)

    def _on_upgrade_bought(self, upgrade_name: str):
        """Triggered after an upgrade is purchased."""
        self.upgrade_store.apply_upgrade_bonuses()

        # Save using the callback if provided
        if self.save_callback:
            self.save_callback()

        # Optionally refresh UI tabs if needed
        if hasattr(self.materials_tab, 'refresh_ui'):
            self.materials_tab.refresh_ui()
        if hasattr(self.power_tab, 'refresh_ui'):
            self.power_tab.refresh_ui()
        if hasattr(self.crafting_tab, 'refresh_ui'):
            self.crafting_tab.refresh_ui()
        if hasattr(self.upgrades_tab, 'refresh_ui'):
            self.upgrades_tab.refresh_ui()