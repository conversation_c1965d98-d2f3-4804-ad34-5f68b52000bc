from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON><PERSON>ow, QTabWidget, QWidget, QVBoxLayout
from apps.gClicker.app.ui.materials_tab import MaterialsTab
from apps.gClicker.app.ui.power_tab import PowerTab
from apps.gClicker.app.ui.crafting_tab import CraftingTab
from apps.gClicker.app.ui.upgrades_tab import UpgradesTab

from apps.gClicker.app.core.materials.manager import MaterialManager
from apps.gClicker.app.core.power.manager import PowerManager
from apps.gClicker.app.core.crafting.manager import CraftingManager
from apps.gClicker.app.core.economy.upgrade_store import UpgradeStore

from apps.gClicker.app.state.save_manager import save_user_data


class MainView(QMainWindow):
    def __init__(self, user_data: dict):
        super().__init__()
        self.setWindowTitle("gClicker")

        self.user_data = user_data

        # Core managers
        self.material_manager = MaterialManager(user_data.get("materials", {}))
        self.power_manager = PowerManager(user_data.get("power", {}))
        self.crafting_manager = CraftingManager(self.material_manager)
        self.upgrade_store = UpgradeStore(user_data)

        self._init_ui()

    def _init_ui(self):
        tabs = QTabWidget()

        # Tabs
        self.materials_tab = MaterialsTab(self.material_manager)
        self.power_tab = PowerTab(self.power_manager)
        self.crafting_tab = CraftingTab(self.crafting_manager)
        self.upgrades_tab = UpgradesTab(self.upgrade_store, self._on_upgrade_bought)

        tabs.addTab(self.materials_tab, "Materials")
        tabs.addTab(self.power_tab, "Power")
        tabs.addTab(self.crafting_tab, "Crafting")
        tabs.addTab(self.upgrades_tab, "Upgrades")

        # Layout
        central_widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(tabs)
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)

    def _on_upgrade_bought(self, upgrade_name: str):
        """Triggered after an upgrade is purchased."""
        self.upgrade_store.apply_upgrade_bonuses()
        save_user_data(self.user_data)

        # Optionally refresh UI tabs if needed
        self.materials_tab.refresh_ui()
        self.power_tab.refresh_ui()
        self.crafting_tab.refresh_ui()
        self.upgrades_tab.refresh_ui()