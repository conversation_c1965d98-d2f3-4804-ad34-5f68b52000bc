# apps/gClicker/app/ui/power_tab.py

from PyQt6.QtWidgets import <PERSON>Widget, QVBoxLayout, QLabel, QPushButton, QProgressBar, QHBoxLayout
from PyQt6.QtCore import QTimer


class PowerTab(QWidget):
    def __init__(self, power_manager, save_callback=None):
        super().__init__()

        self.power_manager = power_manager
        self.save_callback = save_callback

        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        self.power_label = QLabel()
        self.max_power_label = QLabel()
        self.regen_rate_label = QLabel()

        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(self.power_manager.get_max_power())

        self.consume_button = QPushButton("Consume 10 Power")
        self.consume_button.clicked.connect(self.consume_power)

        self.layout.addWidget(self.power_label)
        self.layout.addWidget(self.max_power_label)
        self.layout.addWidget(self.regen_rate_label)
        self.layout.addWidget(self.progress_bar)
        self.layout.addWidget(self.consume_button)

        # Setup timer to auto regen power every second
        self.timer = QTimer(self)
        self.timer.setInterval(1000)  # 1 second
        self.timer.timeout.connect(self.regen_power)
        self.timer.start()

        self.refresh_ui()

    def refresh_ui(self):
        current = self.power_manager.get_power()
        maximum = self.power_manager.get_max_power()
        regen_rate = self.power_manager.user_data["power"].get("regen_rate", 0)

        self.power_label.setText(f"Current Power: {current}")
        self.max_power_label.setText(f"Max Power: {maximum}")
        self.regen_rate_label.setText(f"Regen Rate: {regen_rate} per second")
        self.progress_bar.setMaximum(maximum)
        self.progress_bar.setValue(current)

        if self.save_callback:
            self.save_callback()

    def consume_power(self):
        if self.power_manager.consume_power(10):
            self.refresh_ui()
        else:
            # Could add user feedback here that there's not enough power
            pass

    def regen_power(self):
        self.power_manager.regen_power()
        self.refresh_ui()
