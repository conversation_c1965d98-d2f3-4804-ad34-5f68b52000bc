from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QListWidget, QPushButton, QMessageBox, QHBoxLayout
)
from PyQt6.QtCore import Qt
from apps.gClicker.app.core.crafting.manager import CraftingManager

class CraftingTab(QWidget):
    def __init__(self, user_data: dict, power_pool: dict, parent=None):
        super().__init__(parent)
        self.user_data = user_data
        self.power_pool = power_pool
        self.crafting_manager = CraftingManager(self.user_data, self.power_pool)
        self.blueprints = self.crafting_manager.get_all_blueprints()

        self.layout = QVBoxLayout(self)

        self.title_label = QLabel("Crafting")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(self.title_label)

        # List of blueprints
        self.blueprint_list = QListWidget()
        self.blueprint_list.addItems(sorted(self.blueprints.keys()))
        self.blueprint_list.currentTextChanged.connect(self.on_blueprint_selected)
        self.layout.addWidget(self.blueprint_list)

        # Details: Required materials & power
        self.details_label = QLabel("Select a blueprint to see details")
        self.details_label.setWordWrap(True)
        self.layout.addWidget(self.details_label)

        # Craft button
        self.craft_button = QPushButton("Craft")
        self.craft_button.clicked.connect(self.on_craft_clicked)
        self.craft_button.setEnabled(False)
        self.layout.addWidget(self.craft_button)

        self.current_blueprint_name = None

    def on_blueprint_selected(self, blueprint_name: str):
        self.current_blueprint_name = blueprint_name
        blueprint = self.blueprints.get(blueprint_name)
        if not blueprint:
            self.details_label.setText("No details available.")
            self.craft_button.setEnabled(False)
            return

        materials_list = "\n".join(
            f"{mat}: {qty}" for mat, qty in blueprint.required_materials.items()
        )
        power = blueprint.power_cost or 0
        time_sec = blueprint.crafting_time_seconds or 0

        details_text = (
            f"Required Materials:\n{materials_list}\n"
            f"Power Cost: {power}\n"
            f"Crafting Time: {time_sec:.2f} sec"
        )
        self.details_label.setText(details_text)

        # Enable craft button if user has enough materials & power
        can_craft = self.crafting_manager.can_craft(blueprint_name)
        self.craft_button.setEnabled(can_craft)

    def on_craft_clicked(self):
        if not self.current_blueprint_name:
            return

        success = self.crafting_manager.craft(self.current_blueprint_name)
        if success:
            QMessageBox.information(self, "Crafting", f"Successfully crafted {self.current_blueprint_name}!")
        else:
            QMessageBox.warning(self, "Crafting Failed", "Insufficient materials or power.")

        # Refresh button state in case inventory/power changed
        self.on_blueprint_selected(self.current_blueprint_name)