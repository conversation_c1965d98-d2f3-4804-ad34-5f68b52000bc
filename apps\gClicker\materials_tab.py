from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QProgressBar, QScrollArea, QFrame, QCheckBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from .material_manager import MaterialManager

class MaterialCard(QFrame):
    def __init__(self, material, material_manager, on_update_callback):
        super().__init__()
        self.material = material
        self.material_manager = material_manager
        self.on_update_callback = on_update_callback
        
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet("""
            QFrame {
                border: 2px solid #3A86FF;
                border-radius: 8px;
                padding: 10px;
                background-color: #1E1E1E;
            }
            QFrame:hover {
                border-color: #60AFFF;
            }
        """)
        
        self._init_ui()
    
    def _init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # Material name and tier
        name_label = QLabel(self.material.name.title())
        name_label.setFont(QFont("Segoe UI", 16, QFont.Weight.Bold))
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(name_label)
        
        tier_label = QLabel(f"Tier: {self.material.tier.value.title()}")
        tier_label.setFont(QFont("Segoe UI", 10))
        tier_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(tier_label)
        
        if not self.material.unlocked:
            unlock_btn = QPushButton(f"Unlock Machine ({self.material.unlock_cost})")
            unlock_btn.clicked.connect(self._unlock_material)
            layout.addWidget(unlock_btn)
            self.unlock_btn = unlock_btn
            return
        
        # Production rate
        rate_label = QLabel(f"Production: {self.material.get_production_rate()}/sec")
        rate_label.setFont(QFont("Segoe UI", 12))
        layout.addWidget(rate_label)
        
        # Storage display
        storage_frame = QFrame()
        storage_layout = QVBoxLayout()
        storage_frame.setLayout(storage_layout)
        
        # Raw storage
        raw_layout = QHBoxLayout()
        raw_label = QLabel("Raw:")
        raw_label.setFont(QFont("Segoe UI", 10))
        self.raw_amount_label = QLabel(f"{self.material.storage['raw']}/{self.material.storage_capacity}")
        self.raw_progress = QProgressBar()
        self.raw_progress.setMaximum(100)
        self.raw_progress.setValue(self.material.get_storage_usage()["raw"])
        
        raw_layout.addWidget(raw_label)
        raw_layout.addWidget(self.raw_amount_label)
        raw_layout.addWidget(self.raw_progress)
        storage_layout.addLayout(raw_layout)
        
        # Refined storage (if available)
        if self.material.can_refine:
            refined_layout = QHBoxLayout()
            refined_label = QLabel("Refined:")
            refined_label.setFont(QFont("Segoe UI", 10))
            self.refined_amount_label = QLabel(f"{self.material.storage['refined']}/{self.material.storage_capacity}")
            self.refined_progress = QProgressBar()
            self.refined_progress.setMaximum(100)
            self.refined_progress.setValue(self.material.get_storage_usage()["refined"])
            refined_layout.addWidget(refined_label)
            refined_layout.addWidget(self.refined_amount_label)
            refined_layout.addWidget(self.refined_progress)
            storage_layout.addLayout(refined_layout)
            # Show refinement rate
            refine_rate_label = QLabel(f"Refinement Rate: {self.material.refine_rate}/sec (Consumes {self.material.refine_conversion_rate} raw → 1 refined)")
            refine_rate_label.setFont(QFont("Segoe UI", 10))
            layout.addWidget(refine_rate_label)
            # Add auto-refine toggle
            self.auto_refine_checkbox = QCheckBox("Auto-Refine")
            self.auto_refine_checkbox.setChecked(self.material.auto_refine_enabled)
            self.auto_refine_checkbox.stateChanged.connect(self._toggle_auto_refine)
            layout.addWidget(self.auto_refine_checkbox)
        # Pure storage (if available)
        if self.material.can_purify:
            pure_layout = QHBoxLayout()
            pure_label = QLabel("Pure:")
            pure_label.setFont(QFont("Segoe UI", 10))
            self.pure_amount_label = QLabel(f"{self.material.storage['pure']}/{self.material.storage_capacity}")
            self.pure_progress = QProgressBar()
            self.pure_progress.setMaximum(100)
            self.pure_progress.setValue(self.material.get_storage_usage()["pure"])
            pure_layout.addWidget(pure_label)
            pure_layout.addWidget(self.pure_amount_label)
            pure_layout.addWidget(self.pure_progress)
            storage_layout.addLayout(pure_layout)
            # Show purification rate
            pure_rate_label = QLabel(f"Purification Rate: {self.material.pure_rate}/sec (Consumes {self.material.purify_conversion_rate} refined → 1 pure)")
            pure_rate_label.setFont(QFont("Segoe UI", 10))
            layout.addWidget(pure_rate_label)
            # Add auto-purify toggle
            self.auto_purify_checkbox = QCheckBox("Auto-Purify")
            self.auto_purify_checkbox.setChecked(self.material.auto_purify_enabled)
            self.auto_purify_checkbox.stateChanged.connect(self._toggle_auto_purify)
            layout.addWidget(self.auto_purify_checkbox)
        
        # Alloying controls (if available)
        if hasattr(self.material, 'can_alloy') and self.material.can_alloy:
            alloy_rate_label = QLabel(f"Alloying Rate: {self.material.alloying_rate}/sec")
            alloy_rate_label.setFont(QFont("Segoe UI", 10))
            layout.addWidget(alloy_rate_label)
            # Add auto-alloy toggle
            self.auto_alloy_checkbox = QCheckBox("Auto-Alloy")
            self.auto_alloy_checkbox.setChecked(self.material.auto_alloy_enabled)
            self.auto_alloy_checkbox.stateChanged.connect(self._toggle_auto_alloy)
            layout.addWidget(self.auto_alloy_checkbox)
        
        layout.addWidget(storage_frame)
        
        # Upgrades section
        upgrades_label = QLabel("Upgrades:")
        upgrades_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        layout.addWidget(upgrades_label)
        
        # Upgrade buttons
        self.upgrade_buttons = {}
        for upgrade in self.material.get_available_upgrades():
            if upgrade.name not in self.material.upgrades_owned:
                # Format cost string
                if isinstance(upgrade.cost, int):
                    cost_str = f"{upgrade.cost} currency"
                elif isinstance(upgrade.cost, tuple):
                    c, (mat, state, amt) = upgrade.cost
                    cost_str = f"{c} currency, {amt} {state} {mat}"
                else:
                    cost_str = str(upgrade.cost)
                btn = QPushButton(f"{upgrade.name} ({cost_str})")
                btn.setToolTip(upgrade.description)
                btn.clicked.connect(lambda checked, u=upgrade: self._buy_upgrade(u))
                self.upgrade_buttons[upgrade.name] = btn
                layout.addWidget(btn)
            else:
                owned_label = QLabel(f"{upgrade.name} (OWNED)")
                owned_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
                layout.addWidget(owned_label)
        
        self._update_buttons()
    
    def _produce(self):
        produced = self.material_manager.produce_material(self.material.name.lower())
        if produced > 0:
            self._update_display()
            self.on_update_callback()
    
    def _refine_all(self):
        if self.material.can_refine:
            refined = self.material_manager.refine_material(self.material.name.lower())
            if refined > 0:
                self._update_display()
                self.on_update_callback()
    
    def _buy_upgrade(self, upgrade):
        if self.material_manager.buy_material_upgrade(self.material.name.lower(), upgrade.name):
            self._update_display()
            self._update_buttons()
            self.on_update_callback()
    
    def _update_display(self):
        # Update storage labels and progress bars
        self.raw_amount_label.setText(f"{self.material.storage['raw']}/{self.material.storage_capacity}")
        self.raw_progress.setValue(self.material.get_storage_usage()["raw"])
        
        # Only update refined labels if they exist
        if hasattr(self, 'refined_amount_label') and hasattr(self, 'refined_progress'):
            self.refined_amount_label.setText(f"{self.material.storage['refined']}/{self.material.storage_capacity}")
            self.refined_progress.setValue(self.material.get_storage_usage()["refined"])
        
        # Update production rate
        for i in range(self.layout().count()):
            widget = self.layout().itemAt(i).widget()
            if isinstance(widget, QLabel) and "Production:" in widget.text():
                widget.setText(f"Production: {self.material.get_production_rate()}/sec")
                break
    
    def _update_buttons(self):
        currency = self.material_manager.get_currency()
        
        # Update upgrade buttons
        for upgrade_name, btn in self.upgrade_buttons.items():
            if upgrade_name in self.material.upgrades_owned:
                btn.setText(f"{upgrade_name} (OWNED)")
                btn.setEnabled(False)
            else:
                # Find upgrade cost
                upgrade = None
                for available_upgrade in self.material.get_available_upgrades():
                    if available_upgrade.name == upgrade_name:
                        upgrade = available_upgrade
                        break
                
                if upgrade:
                    # Format cost string
                    if isinstance(upgrade.cost, int):
                        cost_str = f"{upgrade.cost} currency"
                        enabled = currency >= upgrade.cost
                    elif isinstance(upgrade.cost, tuple):
                        c, (mat, state, amt) = upgrade.cost
                        cost_str = f"{c} currency, {amt} {state} {mat}"
                        enabled = currency >= c and self.material_manager.has_material(mat, state, amt)
                    else:
                        cost_str = str(upgrade.cost)
                        enabled = False
                    btn.setText(f"{upgrade_name} ({cost_str})")
                    btn.setEnabled(enabled)
    
    def _unlock_material(self):
        if self.material_manager.unlock_material(self.material.name.lower()):
            self.material.unlocked = True
            self.on_update_callback()
            self._refresh_card()
    
    def _refresh_card(self):
        # Remove all widgets and re-init UI
        while self.layout().count():
            item = self.layout().takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        self._init_ui()

    def _toggle_auto_refine(self, state):
        self.material.auto_refine_enabled = bool(state)
        self.material_manager._save_material_data(self.material.name.lower())
        self.on_update_callback()

    def _toggle_auto_purify(self, state):
        self.material.auto_purify_enabled = bool(state)
        self.material_manager._save_material_data(self.material.name.lower())
        self.on_update_callback()

    def _toggle_auto_alloy(self, state):
        self.material.auto_alloy_enabled = bool(state)
        self.material_manager._save_material_data(self.material.name.lower())
        self.on_update_callback()

class MaterialsTab(QWidget):
    def __init__(self, material_manager: MaterialManager, on_update_callback=None):
        super().__init__()
        self.material_manager = material_manager
        self.on_update_callback = on_update_callback
        self.material_cards = {}
        
        self._init_ui()
    
    def _init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # Title
        title = QLabel("Materials Production")
        title.setFont(QFont("Segoe UI", 20, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Currency display
        self.currency_label = QLabel(f"Currency: {self.material_manager.get_currency()}")
        self.currency_label.setFont(QFont("Segoe UI", 14))
        self.currency_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.currency_label)
        
        # Materials grid
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        materials_widget = QWidget()
        self.materials_layout = QVBoxLayout()
        materials_widget.setLayout(self.materials_layout)
        scroll_area.setWidget(materials_widget)
        
        self._load_material_cards()
    
    def _load_material_cards(self):
        # Clear existing cards
        for card in self.material_cards.values():
            self.materials_layout.removeWidget(card)
            card.deleteLater()
        self.material_cards.clear()
        
        # Create cards for each material
        materials = self.material_manager.get_all_materials()
        for material in materials:
            card = MaterialCard(material, self.material_manager, self._on_material_update)
            self.material_cards[material.name] = card
            self.materials_layout.addWidget(card)
    
    def _on_material_update(self):
        """Called when a material is updated"""
        self._update_currency_display()
        self._load_material_cards()  # Always reload cards to avoid attribute errors
        if self.on_update_callback:
            self.on_update_callback()
    
    def _update_currency_display(self):
        self.currency_label.setText(f"Currency: {self.material_manager.get_currency()}")
    
    def _update_all_cards(self):
        for card in self.material_cards.values():
            card._update_display()
            card._update_buttons()
    
    def refresh(self):
        """Refresh the entire tab"""
        self._update_currency_display()
        self._load_material_cards()  # Always reload cards
    
    def _update_all_cards(self):
        for card in self.material_cards.values():
            card._update_display()
            card._update_buttons() 