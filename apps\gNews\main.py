from sdk.app_base import G<PERSON>AppBase
from PyQt6.QtWidgets import <PERSON><PERSON>ox<PERSON>ayout, QLabel, QPushButton
from PyQt6.QtGui import QFont, QIcon
from PyQt6.QtCore import Qt
import os
from .news.manager import NewsManager
from .ui.main_view import NewsFeedView
from .ui.article_editor import ArticleEditor

ADMIN_USERS = ["Solomon"]  # Add admin usernames here

class GNews(GNetAppBase):
    app_name = "gNews"
    app_version = "1.0.0"
    app_description = "Official news, patch notes, and announcements for gNet."
    app_icon = QIcon(os.path.join(os.path.dirname(__file__), "news_icon.png")) if os.path.exists(os.path.join(os.path.dirname(__file__), "news_icon.png")) else None

    def __init__(self, user_account):
        super().__init__(user_account)
        self.news_manager = NewsManager()
        self._init_ui()

    def _init_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        title = QLabel("gNews")
        title.setFont(QFont("Segoe UI", 24))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        self.feed_view = NewsFeedView(self.news_manager, user=self.user)
        layout.addWidget(self.feed_view)

        if self.user.username in ADMIN_USERS:
            self.editor_btn = QPushButton("Post News Article (Admin)")
            self.editor_btn.clicked.connect(self.open_editor)
            layout.addWidget(self.editor_btn)
            self.editor = None

    def open_editor(self):
        if not self.editor:
            self.editor = ArticleEditor(self.news_manager, self.user.username, on_article_added=self.refresh_feed)
        self.editor.show()

    def refresh_feed(self):
        self.feed_view.refresh_articles() 