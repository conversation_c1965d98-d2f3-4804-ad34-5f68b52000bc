# apps.gClicker.app.core.economy.upgrade_store.py

class UpgradeStore:
    """
    Handles upgrades: purchasing, listing, and calculating their effects.
    """

    UPGRADES = [
        {"name": "Iron Finger",       "power": 1,              "cost": 50},
        {"name": "Steel Knuckle",     "power": 5,              "cost": 200},
        {"name": "Titanium Tap",      "power": 10,             "cost": 750},
        {"name": "Lucky Tap",         "crit_chance": 5,        "cost": 500},
        {"name": "Crit Mastery",      "crit_multiplier": 2,    "cost": 1500},
        {"name": "Finger Drone",      "passive_income": 2,     "cost": 100},
        {"name": "Nano Tapper",       "passive_income": 5,     "cost": 250},
        {"name": "Auto Tapper",       "passive_income": 10,    "cost": 500},
        {"name": "Giga Click Farm",   "passive_income": 15,    "cost": 1250},
    ]

    def __init__(self, user_data: dict):
        self.user_data = user_data
        self.currency_key = "currency"
        self.upgrades_key = "upgrades_owned"

        if self.currency_key not in self.user_data:
            self.user_data[self.currency_key] = 0
        if self.upgrades_key not in self.user_data:
            self.user_data[self.upgrades_key] = []

    def get_currency(self) -> int:
        return self.user_data.get(self.currency_key, 0)

    def get_owned_upgrades(self) -> list:
        return self.user_data.get(self.upgrades_key, [])

    def get_available_upgrades(self) -> list:
        return [u for u in self.UPGRADES if u["name"] not in self.get_owned_upgrades()]

    def get_upgrade_by_name(self, name: str) -> dict | None:
        for upgrade in self.UPGRADES:
            if upgrade["name"] == name:
                return upgrade
        return None

    def can_purchase(self, name: str) -> bool:
        upgrade = self.get_upgrade_by_name(name)
        if not upgrade:
            return False
        if name in self.get_owned_upgrades():
            return False
        return self.get_currency() >= upgrade["cost"]

    def purchase_upgrade(self, name: str) -> bool:
        """
        Attempts to purchase an upgrade. Returns True on success, False otherwise.
        """
        if not self.can_purchase(name):
            return False

        upgrade = self.get_upgrade_by_name(name)
        self.user_data[self.currency_key] -= upgrade["cost"]
        self.user_data[self.upgrades_key].append(name)
        return True

    def get_upgrade_effects(self) -> dict:
        """
        Combines all active upgrade effects into a single dict:
        {
            "power": int,
            "crit_chance": int,
            "crit_multiplier": int,
            "passive_income": int
        }
        """
        effects = {
            "power": 0,
            "crit_chance": 0,
            "crit_multiplier": 1,  # default is 1x
            "passive_income": 0
        }

        for name in self.get_owned_upgrades():
            upgrade = self.get_upgrade_by_name(name)
            if not upgrade:
                continue

            for key in ["power", "crit_chance", "crit_multiplier", "passive_income"]:
                if key in upgrade:
                    if key == "crit_multiplier":
                        effects[key] = max(effects[key], upgrade[key])
                    else:
                        effects[key] += upgrade[key]

        return effects