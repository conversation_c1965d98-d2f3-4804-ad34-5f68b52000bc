import hashlib
import datetime
import user.storage as storage

class User:
    """
    Represents a user account with authentication, persistent data storage, and transaction logging.

    Attributes:
        username (str): The unique username of the user.
        password_hash (str): The hashed password stored securely.
        data (dict): A dictionary holding user-related data including username, password_hash, gBalance,
                     and any other custom fields.
    """

    def __init__(self, username: str, password_plain: str | None = None, data: dict | None = None):
        """
        Initializes a User instance.

        Args:
            username (str): The username for the user.
            password_plain (str | None): Plaintext password to hash and store (if creating new user).
            data (dict | None): Existing user data dictionary to load (if loading existing user).

        Raises:
            ValueError: If neither password nor data is provided for a new user.
        """
        if data:
            self.username = data["username"]
            self.password_hash = data["password_hash"]
            self.data = data  # Loaded user data dict
        else:
            if password_plain is None:
                raise ValueError("Password required when creating a new user")
            self.username = username
            self.password_hash = self._hash_password(password_plain)
            self.data = {
                "username": username,
                "password_hash": self.password_hash,
                "gBalance": 10.0,
            }

    def _hash_password(self, password: str) -> str:
        """
        Hashes a plaintext password using SHA-256.

        Args:
            password (str): The plaintext password.

        Returns:
            str: The hex digest of the SHA-256 hash.
        """
        return hashlib.sha256(password.encode()).hexdigest()

    def check_password(self, password: str) -> bool:
        """
        Checks if the given plaintext password matches the stored password hash.

        Args:
            password (str): The plaintext password to verify.

        Returns:
            bool: True if the password matches, False otherwise.
        """
        return self.password_hash == self._hash_password(password)

    def to_dict(self) -> dict:
        """
        Returns the internal user data dictionary.

        Returns:
            dict: The user data dictionary.
        """
        return self.data

    def save(self) -> None:
        """
        Persists the user data using the storage module.
        """
        storage.save_user(self)

    def set_field(self, key: str, value) -> None:
        """
        Sets a value in the user data and saves the user.

        Args:
            key (str): The key in the data dictionary.
            value: The value to set.
        """
        if key == "gBalance":
            value = round(value, 2)
        self.data[key] = value
        self.save()

    def get_field(self, key: str, default=None):
        """
        Retrieves a value from user data by key.

        Args:
            key (str): The key to retrieve.
            default: The default value if the key does not exist.

        Returns:
            The value associated with the key, or default if not found.
        """
        return self.data.get(key, default)

    def log_transaction(self, type_: str, amount: float, description: str | None = None) -> None:
        """
        Logs a transaction with type, amount, timestamp, and optional description.

        Args:
            type_ (str): The type/category of the transaction (e.g., 'deposit', 'withdrawal').
            amount (float): The amount involved in the transaction.
            description (str | None): Optional descriptive text for the transaction.
        """
        timestamp = datetime.datetime.utcnow().isoformat() + "Z"
        if description is None:
            description = f"{type_.capitalize()} of {amount} on user {self.username}"
        transaction = {
            "type": type_,
            "amount": amount,
            "timestamp": timestamp,
            "description": description,
        }
        storage.append_transaction(self.username, transaction)

    def increment_field(self, key: str, amount: float = 1) -> None:
        """
        Increments a numeric field in user data by a specified amount and saves.

        Args:
            key (str): The key to increment.
            amount (float): The amount to increment by (default is 1).
        """
        self.data[key] = round(self.data.get(key, 0) + amount, 2)
        self.save()

    def decrement_field(self, key: str, amount: float = 1, allow_negative: bool = False) -> None:
        """
        Decrements a numeric field by a specified amount and saves.

        Args:
            key (str): The key to decrement.
            amount (float): The amount to decrement by (default is 1).
            allow_negative (bool): If False, raises ValueError if the result is negative.

        Raises:
            ValueError: If `allow_negative` is False and decrement causes negative value.
        """
        current = self.data.get(key, 0)
        new_value = round(current - amount, 2)

        if not allow_negative and new_value < 0:
            raise ValueError(f"'{key}' cannot go negative (attempted {new_value})")

        self.data[key] = new_value
        self.save()